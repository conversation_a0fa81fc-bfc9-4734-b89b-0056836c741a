print("🔄 Cross-Encoder vs Bi-Encoder 对比")
print("=" * 60)
print("Cross-Encoder (交叉编码器):")
print("- 输入: [CLS] query [SEP] document [SEP]")
print("- 编码: 联合编码，query和document可以相互注意")
print("- 输出: 单个相关性分数")
print("- 优点: 精度高，能捕获细粒度交互")
print("- 缺点: 计算成本高，无法预计算文档表示")
print()
print("Bi-Encoder (双编码器，如DPR):")
print("- 输入: query和document分别编码")
print("- 编码: 独立编码，无交互")
print("- 输出: 两个向量，通过点积计算相似度")
print("- 优点: 可预计算，检索速度快")
print("- 缺点: 精度相对较低")

import torch
import torch.nn as nn
from modelscope import AutoTokenizer, AutoModel
import numpy as np

class CrossEncoder(nn.Module):
    """简单的Cross-Encoder实现"""
    
    def __init__(self, model_name="damo/nlp_structbert_backbone_base_std"):
        super().__init__()
        print(f"正在加载模型: {model_name}")
        self.tokenizer = AutoTokenizer.from_pretrained(model_name)
        self.encoder = AutoModel.from_pretrained(model_name)
        
        # 分类头：将[CLS]表示映射到相关性分数
        self.classifier = nn.Linear(self.encoder.config.hidden_size, 1)
        
    def forward(self, query, document):
        """
        前向传播
        Args:
            query: 查询文本
            document: 文档文本
        Returns:
            相关性分数
        """
        # 构造输入：[CLS] query [SEP] document [SEP]
        inputs = self.tokenizer(
            query, document,
            padding=True,
            truncation=True,
            max_length=512,
            return_tensors="pt"
        )
        
        # 联合编码
        outputs = self.encoder(**inputs)
        
        # 使用[CLS] token的表示
        cls_representation = outputs.last_hidden_state[:, 0, :]  # [batch_size, hidden_size]
        
        # 计算相关性分数
        relevance_score = self.classifier(cls_representation)  # [batch_size, 1]
        
        return relevance_score.squeeze(-1)  # [batch_size]

# 创建模型实例
try:
    cross_encoder = CrossEncoder()
    print("✅ Cross-Encoder模型创建完成")
    print(f"模型参数量: {sum(p.numel() for p in cross_encoder.parameters()):,}")
except Exception as e:
    print(f"❌ 模型加载失败: {e}")
    print("尝试使用备用模型...")
    # 备用方案：使用transformers库
    from transformers import AutoTokenizer, AutoModel
    
    class CrossEncoderBackup(nn.Module):
        def __init__(self, model_name="bert-base-chinese"):
            super().__init__()
            print(f"正在加载备用模型: {model_name}")
            self.tokenizer = AutoTokenizer.from_pretrained(model_name)
            self.encoder = AutoModel.from_pretrained(model_name)
            self.classifier = nn.Linear(self.encoder.config.hidden_size, 1)
            
        def forward(self, query, document):
            inputs = self.tokenizer(
                query, document,
                padding=True,
                truncation=True,
                max_length=512,
                return_tensors="pt"
            )
            outputs = self.encoder(**inputs)
            cls_representation = outputs.last_hidden_state[:, 0, :]
            relevance_score = self.classifier(cls_representation)
            return relevance_score.squeeze(-1)
    
    cross_encoder = CrossEncoderBackup()
    print("✅ 备用Cross-Encoder模型创建完成")
    print(f"模型参数量: {sum(p.numel() for p in cross_encoder.parameters()):,}")

def demonstrate_cross_encoder():
    """演示Cross-Encoder的工作流程"""
    
    # 示例数据
    query = "什么是机器学习？"
    documents = [
        "机器学习是人工智能的一个分支，它使计算机能够在没有明确编程的情况下学习。",
        "深度学习是机器学习的一个子集，使用神经网络来模拟人脑的工作方式。",
        "今天天气很好，适合出去散步。"
    ]
    
    print("🔍 Cross-Encoder检索演示")
    print(f"查询: {query}")
    print("\n候选文档:")
    for i, doc in enumerate(documents):
        print(f"{i+1}. {doc}")
    
    print("\n🔄 处理过程:")
    
    # 对每个文档计算相关性分数
    scores = []
    with torch.no_grad():
        for i, doc in enumerate(documents):
            print(f"\n处理文档 {i+1}:")
            
            # 构造输入
            inputs = cross_encoder.tokenizer(
                query, doc,
                padding=True,
                truncation=True,
                max_length=512,
                return_tensors="pt"
            )
            
            # 显示前20个token
            tokens = cross_encoder.tokenizer.decode(inputs['input_ids'][0][:20])
            print(f"  输入tokens: {tokens}...")
            
            # 计算分数
            score = cross_encoder(query, doc)
            scores.append(score.item())
            
            print(f"  相关性分数: {score.item():.4f}")
    
    # 排序结果
    ranked_results = sorted(zip(documents, scores), key=lambda x: x[1], reverse=True)
    
    print("\n📊 排序结果:")
    for i, (doc, score) in enumerate(ranked_results):
        print(f"{i+1}. 分数: {score:.4f} | {doc[:50]}...")
    
    return ranked_results

# 运行演示
try:
    results = demonstrate_cross_encoder()
except Exception as e:
    print(f"演示过程中出现错误: {e}")
    print("这可能是由于模型加载或推理过程中的问题")

class BiEncoder(nn.Module):
    """简单的Bi-Encoder实现用于对比"""
    
    def __init__(self):
        super().__init__()
        # 使用与Cross-Encoder相同的tokenizer和encoder
        self.tokenizer = cross_encoder.tokenizer
        self.encoder = cross_encoder.encoder
        
    def encode_text(self, texts):
        """编码文本为向量"""
        if isinstance(texts, str):
            texts = [texts]
            
        inputs = self.tokenizer(
            texts,
            padding=True,
            truncation=True,
            max_length=512,
            return_tensors="pt"
        )
        
        with torch.no_grad():
            outputs = self.encoder(**inputs)
            # 使用[CLS] token作为句子表示
            embeddings = outputs.last_hidden_state[:, 0, :]
            
        return embeddings
    
    def compute_similarity(self, query_emb, doc_embs):
        """计算相似度"""
        # 余弦相似度
        query_norm = torch.nn.functional.normalize(query_emb, p=2, dim=1)
        doc_norm = torch.nn.functional.normalize(doc_embs, p=2, dim=1)
        
        similarities = torch.matmul(query_norm, doc_norm.T)
        return similarities.squeeze(0)

def compare_encoders():
    """对比Cross-Encoder和Bi-Encoder"""
    
    query = "机器学习的应用"
    documents = [
        "机器学习在图像识别、自然语言处理等领域有广泛应用。",
        "深度学习是机器学习的重要分支。",
        "今天的午餐很美味。"
    ]
    
    print("⚖️ Cross-Encoder vs Bi-Encoder 对比")
    print("=" * 50)
    
    try:
        # Cross-Encoder结果
        print("\n🔄 Cross-Encoder结果:")
        cross_scores = []
        with torch.no_grad():
            for i, doc in enumerate(documents):
                score = cross_encoder(query, doc)
                cross_scores.append(score.item())
                print(f"文档{i+1}: {score.item():.4f}")
        
        # Bi-Encoder结果
        print("\n🔀 Bi-Encoder结果:")
        bi_encoder = BiEncoder()
        
        query_emb = bi_encoder.encode_text([query])
        doc_embs = bi_encoder.encode_text(documents)
        bi_scores = bi_encoder.compute_similarity(query_emb, doc_embs)
        
        for i, score in enumerate(bi_scores):
            print(f"文档{i+1}: {score.item():.4f}")
        
        print("\n📈 分析:")
        print("- Cross-Encoder通常能提供更精确的相关性判断")
        print("- Bi-Encoder计算速度更快，适合大规模检索")
        print("- 实际应用中常结合使用：Bi-Encoder粗排 + Cross-Encoder精排")
        
    except Exception as e:
        print(f"对比过程中出现错误: {e}")

compare_encoders()

print("⚖️ Cross-Encoder 优缺点分析")
print("=" * 50)

print("✅ 优点:")
advantages = [
    "高精度：能捕获query和document间的细粒度交互",
    "强表达能力：利用注意力机制建模复杂关系",
    "端到端训练：可以针对特定任务进行优化",
    "SOTA性能：在多个基准测试中表现优异"
]

for i, adv in enumerate(advantages, 1):
    print(f"  {i}. {adv}")

print("\n❌ 缺点:")
disadvantages = [
    "计算成本高：每个query-document对都需要重新计算",
    "无法预计算：不能提前计算document表示",
    "扩展性差：不适合大规模实时检索",
    "延迟较高：推理时间随候选文档数量线性增长"
]

for i, dis in enumerate(disadvantages, 1):
    print(f"  {i}. {dis}")

print("\n🎯 最佳实践:")
best_practices = [
    "两阶段检索：Bi-Encoder召回 + Cross-Encoder重排",
    "候选集限制：只对Top-K候选进行Cross-Encoder评分",
    "批处理优化：批量处理多个query-document对",
    "模型蒸馏：将Cross-Encoder知识蒸馏到Bi-Encoder"
]

for i, practice in enumerate(best_practices, 1):
    print(f"  {i}. {practice}")

print("\n🔚 总结:")
print("Cross-Encoder是精度导向的模型，适合对少量候选进行精确排序。")
print("在实际应用中，通常与Bi-Encoder结合使用，发挥各自优势。")

print("🎯 Cross-Encoder的主要应用场景")
print("=" * 50)

applications = {
    "信息检索": {
        "描述": "重排序搜索结果，提高检索精度",
        "流程": "Bi-Encoder粗排 → Cross-Encoder精排",
        "优势": "显著提升Top-K结果的相关性"
    },
    "问答系统": {
        "描述": "判断候选答案与问题的匹配度",
        "流程": "问题+候选答案 → 相关性分数",
        "优势": "能理解问答间的语义关系"
    },
    "文本匹配": {
        "描述": "判断两个文本的语义相似性",
        "流程": "文本对 → 匹配分数",
        "优势": "捕获细粒度的语义交互"
    },
    "推荐系统": {
        "描述": "计算用户查询与商品描述的匹配度",
        "流程": "用户意图+商品信息 → 推荐分数",
        "优势": "理解用户需求与商品特征的关联"
    }
}

for app_name, details in applications.items():
    print(f"\n📌 {app_name}:")
    print(f"   描述: {details['描述']}")
    print(f"   流程: {details['流程']}")
    print(f"   优势: {details['优势']}")

print("\n💡 实际部署建议:")
deployment_tips = [
    "使用GPU加速推理",
    "实现批处理以提高吞吐量",
    "设置合理的候选文档数量上限",
    "考虑模型量化以减少内存占用",
    "监控推理延迟和准确性指标"
]

for i, tip in enumerate(deployment_tips, 1):
    print(f"  {i}. {tip}")