# 安装必要的依赖
! pip install torch transformers datasets numpy matpl   otlib

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import matplotlib.pyplot as plt
from transformers import (
    DPRQuestionEncoder, 
    DPRContextEncoder, 
    DPRQuestionEncoderTokenizer, 
    DPRContextEncoderTokenizer
)

print("✓ 所有依赖导入成功")

class SimpleDPR(nn.Module):
    """简化版DPR模型，用于学习理解"""
    
    def __init__(self):
        super().__init__()
        
        # 加载预训练的DPR编码器
        print("正在加载DPR模型...")
        
        # 问题编码器
        self.question_encoder = DPRQuestionEncoder.from_pretrained(
            "facebook/dpr-question_encoder-single-nq-base"
        )
        
        # 文档编码器
        self.context_encoder = DPRContextEncoder.from_pretrained(
            "facebook/dpr-ctx_encoder-single-nq-base"
        )
        
        # 对应的分词器
        self.question_tokenizer = DPRQuestionEncoderTokenizer.from_pretrained(
            "facebook/dpr-question_encoder-single-nq-base"
        )
        self.context_tokenizer = DPRContextEncoderTokenizer.from_pretrained(
            "facebook/dpr-ctx_encoder-single-nq-base"
        )
        
        print("✓ DPR模型加载完成")
    
    def encode_questions(self, questions):
        """编码问题为向量"""
        inputs = self.question_tokenizer(
            questions,
            padding=True,
            truncation=True,
            max_length=512,
            return_tensors="pt"
        )
        
        with torch.no_grad():
            outputs = self.question_encoder(**inputs)
            
        return outputs.pooler_output  # [batch_size, 768]
    
    def encode_contexts(self, contexts):
        """编码文档为向量"""
        inputs = self.context_tokenizer(
            contexts,
            padding=True,
            truncation=True,
            max_length=512,
            return_tensors="pt"
        )
        
        with torch.no_grad():
            outputs = self.context_encoder(**inputs)
            
        return outputs.pooler_output  # [batch_size, 768]
    
    def compute_similarity(self, question_emb, context_emb):
        """计算问题和文档的相似度"""
        # 使用点积计算相似度
        return torch.matmul(question_emb, context_emb.T)

# 初始化模型
dpr_model = SimpleDPR()

# 准备示例数据
questions = [
    "What is the capital of France?",
    "Who invented the telephone?",
    "When was Python programming language created?",
    "What is the largest planet in our solar system?",
    "Who wrote Romeo and Juliet?"
]

contexts = [
    "Paris is the capital and largest city of France, located in the north-central part of the country.",
    "Alexander Graham Bell invented the telephone in 1876, revolutionizing communication.",
    "Python was created by Guido van Rossum and first released in 1991 as a high-level programming language.",
    "Jupiter is the largest planet in our solar system, with a mass greater than all other planets combined.",
    "William Shakespeare wrote the tragedy Romeo and Juliet in the early part of his career.",
    "London is the capital city of England and the United Kingdom.",
    "The iPhone was first released by Apple Inc. in 2007.",
    "Machine learning is a subset of artificial intelligence that focuses on algorithms.",
    "The Great Wall of China is a series of fortifications built across northern China.",
    "Einstein developed the theory of relativity in the early 20th century."
]

print(f"准备了 {len(questions)} 个问题和 {len(contexts)} 个文档")
print("\n示例问题:")
for i, q in enumerate(questions[:3]):
    print(f"{i+1}. {q}")

# 编码问题和文档
print("正在编码问题...")
question_embeddings = dpr_model.encode_questions(questions)

print("正在编码文档...")
context_embeddings = dpr_model.encode_contexts(contexts)

print(f"\n编码结果:")
print(f"问题嵌入维度: {question_embeddings.shape}")
print(f"文档嵌入维度: {context_embeddings.shape}")
print(f"向量维度: {question_embeddings.shape[1]}")

# 计算相似度矩阵
similarity_scores = dpr_model.compute_similarity(question_embeddings, context_embeddings)
print(f"相似度矩阵维度: {similarity_scores.shape}")
print(f"矩阵含义: {len(questions)} 个问题 × {len(contexts)} 个文档")

# 为每个问题找到最相关的文档
print("\n=== 检索结果 ===")
for i, question in enumerate(questions):
    scores = similarity_scores[i]
    
    # 获取top-3最相关的文档
    top_k = 3
    top_indices = torch.topk(scores, k=top_k).indices
    top_scores = torch.topk(scores, k=top_k).values
    
    print(f"\n问题 {i+1}: {question}")
    print("Top-3 匹配文档:")
    
    for rank, (idx, score) in enumerate(zip(top_indices, top_scores)):
        print(f"  {rank+1}. [分数: {score:.3f}] {contexts[idx][:80]}...")

# 可视化相似度矩阵
plt.figure(figsize=(12, 8))
similarity_matrix = similarity_scores.numpy()

# 创建热力图
im = plt.imshow(similarity_matrix, cmap='Blues', aspect='auto')
plt.colorbar(im, label='相似度分数')

# 设置标签
plt.xlabel('文档索引')
plt.ylabel('问题索引')
plt.title('DPR 问题-文档相似度矩阵')

# 添加数值标注
for i in range(len(questions)):
    for j in range(len(contexts)):
        plt.text(j, i, f'{similarity_matrix[i, j]:.2f}', 
                ha='center', va='center', fontsize=8)

plt.tight_layout()
plt.show()

# 显示最高相似度的问题-文档对
max_similarity = torch.max(similarity_scores)
max_pos = torch.argmax(similarity_scores)
max_q_idx = max_pos // len(contexts)
max_c_idx = max_pos % len(contexts)

print(f"\n最高相似度: {max_similarity:.4f}")
print(f"问题: {questions[max_q_idx]}")
print(f"文档: {contexts[max_c_idx]}")

class DPRTrainingDemo:
    """DPR训练过程演示"""
    
    def __init__(self, hidden_size=768):
        self.hidden_size = hidden_size
    
    def simulate_training_batch(self, batch_size=4):
        """模拟一个训练批次"""
        # 模拟问题和正例文档的嵌入
        # 实际训练中这些由编码器生成
        question_emb = torch.randn(batch_size, self.hidden_size)
        positive_doc_emb = torch.randn(batch_size, self.hidden_size)
        
        # 归一化向量（提高训练稳定性）
        question_emb = F.normalize(question_emb, p=2, dim=1)
        positive_doc_emb = F.normalize(positive_doc_emb, p=2, dim=1)
        
        return question_emb, positive_doc_emb
    
    def compute_dpr_loss(self, question_emb, doc_emb, temperature=1.0):
        """计算DPR损失函数"""
        batch_size = question_emb.shape[0]
        
        # 计算相似度矩阵
        similarity_matrix = torch.matmul(question_emb, doc_emb.T) / temperature
        
        # 标签：对角线为正例（每个问题对应自己的文档）
        labels = torch.arange(batch_size)
        
        # 交叉熵损失
        loss = F.cross_entropy(similarity_matrix, labels)
        
        return loss, similarity_matrix, labels
    
    def demonstrate_training(self):
        """演示训练过程"""
        print("=== DPR训练过程演示 ===")
        
        batch_size = 4
        question_emb, doc_emb = self.simulate_training_batch(batch_size)
        
        print(f"批次大小: {batch_size}")
        print(f"问题嵌入: {question_emb.shape}")
        print(f"文档嵌入: {doc_emb.shape}")
        
        # 计算损失
        loss, similarity_matrix, labels = self.compute_dpr_loss(question_emb, doc_emb)
        
        print(f"\n相似度矩阵:")
        print(similarity_matrix.detach().numpy())
        
        print(f"\n正确标签: {labels.numpy()}")
        print(f"模型预测: {torch.argmax(similarity_matrix, dim=1).numpy()}")
        print(f"训练损失: {loss.item():.4f}")
        
        # 计算准确率
        predictions = torch.argmax(similarity_matrix, dim=1)
        accuracy = (predictions == labels).float().mean()
        print(f"批次准确率: {accuracy:.2f}")
        
        return loss, accuracy

# 运行训练演示
trainer = DPRTrainingDemo()
loss, accuracy = trainer.demonstrate_training()

def explain_negative_sampling():
    """解释in-batch negative sampling"""
    
    print("=== In-batch Negative Sampling 详解 ===")
    print()
    
    # 模拟一个批次的训练数据
    batch_questions = [
        "What is the capital of France?",
        "Who invented the telephone?", 
        "When was Python created?",
        "What is the largest planet?"
    ]
    
    batch_positive_docs = [
        "Paris is the capital of France.",
        "Alexander Graham Bell invented the telephone.",
        "Python was created by Guido van Rossum in 1991.",
        "Jupiter is the largest planet in our solar system."
    ]
    
    print("训练批次示例:")
    for i, (q, d) in enumerate(zip(batch_questions, batch_positive_docs)):
        print(f"样本 {i}: Q='{q}' → D='{d}'")
    
    print("\nIn-batch Negative Sampling 策略:")
    print("对于每个问题，其负例文档是批次中其他问题的正例文档")
    print()
    
    for i, question in enumerate(batch_questions):
        print(f"问题 {i}: '{question}'")
        print(f"  正例: '{batch_positive_docs[i]}'")
        print("  负例:")
        for j, neg_doc in enumerate(batch_positive_docs):
            if i != j:
                print(f"    - '{neg_doc}'")
        print()
    
    print("优势:")
    print("1. 无需额外采样负例，提高训练效率")
    print("2. 负例质量较高（都是真实的正例文档）")
    print("3. 批次越大，负例越多，训练效果越好")

explain_negative_sampling()

print("=== 简化版 vs 项目实现对比 ===")
print()

comparison = {
    "特性": ["模型架构", "训练策略", "负采样", "损失函数", "评估指标", "分布式训练", "表格处理"],
    "简化版": [
        "基础双编码器",
        "简单演示", 
        "In-batch sampling",
        "交叉熵损失",
        "准确率",
        "不支持",
        "不支持"
    ],
    "项目实现": [
        "可配置编码器",
        "完整训练流程",
        "多种负采样策略", 
        "多种损失函数",
        "Recall@K等",
        "支持多GPU",
        "专门的表格编码"
    ]
}

import pandas as pd
df = pd.DataFrame(comparison)
print(df.to_string(index=False))

print("\n项目中的高级特性:")
print("1. 支持表格数据的特殊编码（TextBasedTableInput）")
print("2. 多种负采样策略（BM25, 随机采样等）")
print("3. 分布式训练支持")
print("4. 与下游任务的端到端训练")
print("5. 详细的评估指标（Recall@1, @5, @10等）")