{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# DPR (Dense Passage Retrieval) 模型学习教程\n", "\n", "## 1. 什么是DPR？\n", "\n", "DPR是一种基于稠密向量的文档检索方法，主要用于开放域问答任务。它的核心思想是：\n", "\n", "- **双编码器架构**：分别编码问题和文档\n", "- **稠密表示**：使用连续向量而非稀疏关键词\n", "- **相似度匹配**：通过向量相似度找到最相关的文档"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Looking in indexes: https://pypi.tuna.tsinghua.edu.cn/simple\n", "Requirement already satisfied: torch in /Volumes/Data/miniconda/miniconda3/envs/tableqa/lib/python3.8/site-packages (2.4.0)\n", "Requirement already satisfied: transformers in /Volumes/Data/miniconda/miniconda3/envs/tableqa/lib/python3.8/site-packages (4.21.3)\n", "Requirement already satisfied: datasets in /Volumes/Data/miniconda/miniconda3/envs/tableqa/lib/python3.8/site-packages (2.5.2)\n", "Requirement already satisfied: numpy in /Volumes/Data/miniconda/miniconda3/envs/tableqa/lib/python3.8/site-packages (1.24.4)\n", "\u001b[31mERROR: Could not find a version that satisfies the requirement matpl (from versions: none)\u001b[0m\u001b[31m\n", "\u001b[0m\u001b[31mERROR: No matching distribution found for matpl\u001b[0m\u001b[31m\n", "\u001b[0m"]}, {"name": "stderr", "output_type": "stream", "text": ["/Volumes/Data/miniconda/miniconda3/envs/tableqa/lib/python3.8/site-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✓ 所有依赖导入成功\n"]}], "source": ["# 安装必要的依赖\n", "! pip install torch transformers datasets numpy matpl   otlib\n", "\n", "import torch\n", "import torch.nn as nn\n", "import torch.nn.functional as F\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from transformers import (\n", "    DPRQuestionEncoder, \n", "    DPRContextEncoder, \n", "    DPRQuestionEncoderTokenizer, \n", "    DPRContextEncoderTokenizer\n", ")\n", "\n", "print(\"✓ 所有依赖导入成功\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. DPR模型架构\n", "\n", "DPR包含两个独立的BERT编码器：\n", "- **问题编码器 (Question Encoder)**：将问题编码为向量\n", "- **文档编码器 (Context Encoder)**：将文档编码为向量"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'COMMAND_MODE': 'unix2003',\n", " 'CONDA_DEFAULT_ENV': 'tableqa',\n", " 'CONDA_EXE': '/Volumes/Data/miniconda/miniconda3/bin/conda',\n", " 'CONDA_PREFIX': '/Volumes/Data/miniconda/miniconda3/envs/tableqa',\n", " 'CONDA_PROMPT_MODIFIER': '(tableqa) ',\n", " 'CONDA_PYTHON_EXE': '/Volumes/Data/miniconda/miniconda3/bin/python',\n", " 'CONDA_SHLVL': '2',\n", " 'HF_ENDPOINT': 'https://modelscope.cn',\n", " 'HF_HOME': '/Volumes/Data/huggingface',\n", " 'HF_HUB_CACHE': '/Volumes/Data/huggingface/cache',\n", " 'HOME': '/Users/<USER>',\n", " 'HOMEBREW_CELLAR': '/opt/homebrew/Cellar',\n", " 'HOMEBREW_PREFIX': '/opt/homebrew',\n", " 'HOMEBREW_REPOSITORY': '/opt/homebrew',\n", " 'INFOPATH': '/opt/homebrew/share/info:',\n", " 'LESS': '-R',\n", " 'LOGNAME': 'feng',\n", " 'LSCOLORS': 'Gxfxcxdxbxegedabagacad',\n", " 'LS_COLORS': 'di=1;36:ln=35:so=32:pi=33:ex=31:bd=34;46:cd=34;43:su=30;41:sg=30;46:tw=30;42:ow=30;43',\n", " 'MallocNanoZone': '0',\n", " 'OLDPWD': '/',\n", " 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined',\n", " 'PAGER': 'cat',\n", " 'PATH': '/Volumes/Data/miniconda/miniconda3/envs/tableqa/bin:/Volumes/Data/miniconda/miniconda3/condabin:/opt/homebrew/bin:/opt/homebrew/sbin:/Library/Frameworks/Python.framework/Versions/3.11/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Users/<USER>/.cargo/bin',\n", " 'PWD': '/',\n", " 'SHELL': '/bin/zsh',\n", " 'SHLVL': '2',\n", " 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.TIuSIfcZF3/Listeners',\n", " 'TMPDIR': '/var/folders/sw/m2qs9fzs1_l02895b7_ryt080000gn/T/',\n", " 'TRANSFORMERS_CACHE': '/Volumes/Data/huggingface/models',\n", " 'USER': 'feng',\n", " 'VSCODE_CODE_CACHE_PATH': '/Users/<USER>/Library/Application Support/Code/CachedData/6f17636121051a53c88d3e605c491d22af2ba755',\n", " 'VSCODE_CRASH_REPORTER_PROCESS_TYPE': 'extensionHost',\n", " 'VSCODE_CWD': '/',\n", " 'VSCODE_ESM_ENTRYPOINT': 'vs/workbench/api/node/extensionHostProcess',\n", " 'VSCODE_HANDLES_UNCAUGHT_ERRORS': 'true',\n", " 'VSCODE_IPC_HOOK': '/Users/<USER>/Library/Application Support/Code/1.10-main.sock',\n", " 'VSCODE_NLS_CONFIG': '{\"userLocale\":\"zh-cn\",\"osLocale\":\"zh-cn\",\"resolvedLanguage\":\"zh-cn\",\"defaultMessagesFile\":\"/Volumes/Data/Visual Studio Code.app/Contents/Resources/app/out/nls.messages.json\",\"languagePack\":{\"translationsConfigFile\":\"/Users/<USER>/Library/Application Support/Code/clp/70c2bb9a85ce7fd296d22cd2550aebe6.zh-cn/tcf.json\",\"messagesFile\":\"/Users/<USER>/Library/Application Support/Code/clp/70c2bb9a85ce7fd296d22cd2550aebe6.zh-cn/6f17636121051a53c88d3e605c491d22af2ba755/nls.messages.json\",\"corruptMarkerFile\":\"/Users/<USER>/Library/Application Support/Code/clp/70c2bb9a85ce7fd296d22cd2550aebe6.zh-cn/corrupted.info\"},\"locale\":\"zh-cn\",\"availableLanguages\":{\"*\":\"zh-cn\"},\"_languagePackId\":\"70c2bb9a85ce7fd296d22cd2550aebe6.zh-cn\",\"_languagePackSupport\":true,\"_translationsConfigFile\":\"/Users/<USER>/Library/Application Support/Code/clp/70c2bb9a85ce7fd296d22cd2550aebe6.zh-cn/tcf.json\",\"_cacheRoot\":\"/Users/<USER>/Library/Application Support/Code/clp/70c2bb9a85ce7fd296d22cd2550aebe6.zh-cn\",\"_resolvedLanguagePackCoreLocation\":\"/Users/<USER>/Library/Application Support/Code/clp/70c2bb9a85ce7fd296d22cd2550aebe6.zh-cn/6f17636121051a53c88d3e605c491d22af2ba755\",\"_corruptedFile\":\"/Users/<USER>/Library/Application Support/Code/clp/70c2bb9a85ce7fd296d22cd2550aebe6.zh-cn/corrupted.info\"}',\n", " 'VSCODE_PID': '14062',\n", " 'XPC_FLAGS': '0x0',\n", " 'XPC_SERVICE_NAME': '0',\n", " 'ZSH': '/Users/<USER>/.oh-my-zsh',\n", " '_': '/Volumes/Data/miniconda/miniconda3/envs/tableqa/bin/python',\n", " '_CONDA_EXE': '/Volumes/Data/miniconda/miniconda3/bin/conda',\n", " '_CONDA_ROOT': '/Volumes/Data/miniconda/miniconda3',\n", " '__CFBundleIdentifier': 'com.microsoft.VSCode',\n", " '__CF_USER_TEXT_ENCODING': '0x1F5:0x19:0x34',\n", " 'ELECTRON_RUN_AS_NODE': '1',\n", " 'VSCODE_L10N_BUNDLE_LOCATION': 'file:///Users/<USER>/.vscode/extensions/ms-ceintl.vscode-language-pack-zh-hans-1.103.2025081309/translations/extensions/vscode.json-language-features.i18n.json',\n", " 'PYTHONUNBUFFERED': '1',\n", " 'NO_PROXY': 'localhost,127.0.0.1',\n", " 'PYTHONIOENCODING': 'utf-8',\n", " '_CE_CONDA': '',\n", " 'CONDA_PREFIX_1': '/Volumes/Data/miniconda/miniconda3',\n", " 'CONDA_ROOT': '/Volumes/Data/miniconda/miniconda3',\n", " 'HTTPS_PROXY': 'http://127.0.0.1:7897',\n", " '_CE_M': '',\n", " 'HTTP_PROXY': 'http://127.0.0.1:7897',\n", " 'LC_CTYPE': 'UTF-8',\n", " 'PYDEVD_IPYTHON_COMPATIBLE_DEBUGGING': '1',\n", " 'PYTHON_FROZEN_MODULES': 'on',\n", " 'PYDEVD_USE_FRAME_EVAL': 'NO',\n", " 'TERM': 'xterm-color',\n", " 'CLICOLOR': '1',\n", " 'FORCE_COLOR': '1',\n", " 'CLICOLOR_FORCE': '1',\n", " 'GIT_PAGER': 'cat',\n", " 'MPLBACKEND': 'module://matplotlib_inline.backend_inline'}"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["\n", "%env"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["正在加载DPR模型...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Some weights of the model checkpoint at facebook/dpr-question_encoder-single-nq-base were not used when initializing DPRQuestionEncoder: ['question_encoder.bert_model.pooler.dense.bias', 'question_encoder.bert_model.pooler.dense.weight']\n", "- This IS expected if you are initializing DPRQuestionEncoder from the checkpoint of a model trained on another task or with another architecture (e.g. initializing a BertForSequenceClassification model from a BertForPreTraining model).\n", "- This IS NOT expected if you are initializing DPRQuestionEncoder from the checkpoint of a model that you expect to be exactly identical (initializing a BertForSequenceClassification model from a BertForSequenceClassification model).\n", "Some weights of the model checkpoint at facebook/dpr-ctx_encoder-single-nq-base were not used when initializing DPRContextEncoder: ['ctx_encoder.bert_model.pooler.dense.weight', 'ctx_encoder.bert_model.pooler.dense.bias']\n", "- This IS expected if you are initializing DPRContextEncoder from the checkpoint of a model trained on another task or with another architecture (e.g. initializing a BertForSequenceClassification model from a BertForPreTraining model).\n", "- This IS NOT expected if you are initializing DPRContextEncoder from the checkpoint of a model that you expect to be exactly identical (initializing a BertForSequenceClassification model from a BertForSequenceClassification model).\n", "Downloading vocab.txt: 100%|██████████| 226k/226k [00:00<00:00, 10.1MB/s]\n", "Downloading tokenizer.json: 100%|██████████| 455k/455k [00:00<00:00, 6.91MB/s]\n", "Downloading tokenizer_config.json: 100%|██████████| 28.0/28.0 [00:00<00:00, 143kB/s]\n", "Downloading vocab.txt: 100%|██████████| 226k/226k [00:00<00:00, 11.1MB/s]\n", "Downloading tokenizer.json: 100%|██████████| 455k/455k [00:00<00:00, 8.26MB/s]\n", "The tokenizer class you load from this checkpoint is not the same type as the class this function is called from. It may result in unexpected tokenization. \n", "The tokenizer class you load from this checkpoint is 'DPRQuestionEncoderTokenizer'. \n", "The class this function is called from is 'DPRContextEncoderTokenizer'.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✓ DPR模型加载完成\n"]}], "source": ["class SimpleDPR(nn.Module):\n", "    \"\"\"简化版DPR模型，用于学习理解\"\"\"\n", "    \n", "    def __init__(self):\n", "        super().__init__()\n", "        \n", "        # 加载预训练的DPR编码器\n", "        print(\"正在加载DPR模型...\")\n", "        \n", "        # 问题编码器\n", "        self.question_encoder = DPRQuestionEncoder.from_pretrained(\n", "            \"facebook/dpr-question_encoder-single-nq-base\"\n", "        )\n", "        \n", "        # 文档编码器\n", "        self.context_encoder = DPRContextEncoder.from_pretrained(\n", "            \"facebook/dpr-ctx_encoder-single-nq-base\"\n", "        )\n", "        \n", "        # 对应的分词器\n", "        self.question_tokenizer = DPRQuestionEncoderTokenizer.from_pretrained(\n", "            \"facebook/dpr-question_encoder-single-nq-base\"\n", "        )\n", "        self.context_tokenizer = DPRContextEncoderTokenizer.from_pretrained(\n", "            \"facebook/dpr-ctx_encoder-single-nq-base\"\n", "        )\n", "        \n", "        print(\"✓ DPR模型加载完成\")\n", "    \n", "    def encode_questions(self, questions):\n", "        \"\"\"编码问题为向量\"\"\"\n", "        inputs = self.question_tokenizer(\n", "            questions,\n", "            padding=True,\n", "            truncation=True,\n", "            max_length=512,\n", "            return_tensors=\"pt\"\n", "        )\n", "        \n", "        with torch.no_grad():\n", "            outputs = self.question_encoder(**inputs)\n", "            \n", "        return outputs.pooler_output  # [batch_size, 768]\n", "    \n", "    def encode_contexts(self, contexts):\n", "        \"\"\"编码文档为向量\"\"\"\n", "        inputs = self.context_tokenizer(\n", "            contexts,\n", "            padding=True,\n", "            truncation=True,\n", "            max_length=512,\n", "            return_tensors=\"pt\"\n", "        )\n", "        \n", "        with torch.no_grad():\n", "            outputs = self.context_encoder(**inputs)\n", "            \n", "        return outputs.pooler_output  # [batch_size, 768]\n", "    \n", "    def compute_similarity(self, question_emb, context_emb):\n", "        \"\"\"计算问题和文档的相似度\"\"\"\n", "        # 使用点积计算相似度\n", "        return torch.matmul(question_emb, context_emb.T)\n", "\n", "# 初始化模型，创建对象时，会自动下载模型和分词器\n", "dpr_model = SimpleDPR()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. 准备示例数据\n", "\n", "我们准备一些问题和对应的文档来演示DPR的工作原理。"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["准备了 5 个问题和 10 个文档\n", "\n", "示例问题:\n", "1. What is the capital of France?\n", "2. Who invented the telephone?\n", "3. When was Python programming language created?\n"]}], "source": ["# 准备示例数据\n", "questions = [\n", "    \"What is the capital of France?\",\n", "    \"Who invented the telephone?\",\n", "    \"When was Python programming language created?\",\n", "    \"What is the largest planet in our solar system?\",\n", "    \"Who wrote <PERSON> and Juliet?\"\n", "]\n", "\n", "contexts = [\n", "    \"Paris is the capital and largest city of France, located in the north-central part of the country.\",\n", "    \"<PERSON> invented the telephone in 1876, revolutionizing communication.\",\n", "    \"Python was created by <PERSON> and first released in 1991 as a high-level programming language.\",\n", "    \"Jupiter is the largest planet in our solar system, with a mass greater than all other planets combined.\",\n", "    \"<PERSON> wrote the tragedy <PERSON> and <PERSON> in the early part of his career.\",\n", "    \"London is the capital city of England and the United Kingdom.\",\n", "    \"The iPhone was first released by Apple Inc. in 2007.\",\n", "    \"Machine learning is a subset of artificial intelligence that focuses on algorithms.\",\n", "    \"The Great Wall of China is a series of fortifications built across northern China.\",\n", "    \"Einstein developed the theory of relativity in the early 20th century.\"\n", "]\n", "\n", "print(f\"准备了 {len(questions)} 个问题和 {len(contexts)} 个文档\")\n", "print(\"\\n示例问题:\")\n", "for i, q in enumerate(questions[:3]):\n", "    print(f\"{i+1}. {q}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. 编码问题和文档\n", "\n", "使用DPR模型将问题和文档分别编码为向量表示。"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["正在编码问题...\n", "正在编码文档...\n", "\n", "编码结果:\n", "问题嵌入维度: torch.<PERSON><PERSON>([5, 768])\n", "文档嵌入维度: torch.<PERSON><PERSON>([10, 768])\n", "向量维度: 768\n"]}], "source": ["# 编码问题和文档\n", "print(\"正在编码问题...\")\n", "question_embeddings = dpr_model.encode_questions(questions)\n", "\n", "print(\"正在编码文档...\")\n", "context_embeddings = dpr_model.encode_contexts(contexts)\n", "\n", "print(f\"\\n编码结果:\")\n", "print(f\"问题嵌入维度: {question_embeddings.shape}\")\n", "print(f\"文档嵌入维度: {context_embeddings.shape}\")\n", "print(f\"向量维度: {question_embeddings.shape[1]}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. 计算相似度并检索\n", "\n", "计算每个问题与所有文档的相似度，找到最匹配的文档。"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["相似度矩阵维度: torch.<PERSON><PERSON>([5, 10])\n", "矩阵含义: 5 个问题 × 10 个文档\n", "\n", "=== 检索结果 ===\n", "\n", "问题 1: What is the capital of France?\n", "Top-3 匹配文档:\n", "  1. [分数: 86.626] Paris is the capital and largest city of France, located in the north-central pa...\n", "  2. [分数: 69.416] London is the capital city of England and the United Kingdom....\n", "  3. [分数: 54.992] Jupiter is the largest planet in our solar system, with a mass greater than all ...\n", "\n", "问题 2: Who invented the telephone?\n", "Top-3 匹配文档:\n", "  1. [分数: 81.138] <PERSON> invented the telephone in 1876, revolutionizing communicat...\n", "  2. [分数: 65.094] Einstein developed the theory of relativity in the early 20th century....\n", "  3. [分数: 59.371] The iPhone was first released by Apple Inc. in 2007....\n", "\n", "问题 3: When was Python programming language created?\n", "Top-3 匹配文档:\n", "  1. [分数: 91.263] Python was created by <PERSON> and first released in 1991 as a high-leve...\n", "  2. [分数: 64.701] The iPhone was first released by Apple Inc. in 2007....\n", "  3. [分数: 57.161] <PERSON> invented the telephone in 1876, revolutionizing communicat...\n", "\n", "问题 4: What is the largest planet in our solar system?\n", "Top-3 匹配文档:\n", "  1. [分数: 77.258] Jupiter is the largest planet in our solar system, with a mass greater than all ...\n", "  2. [分数: 60.255] Paris is the capital and largest city of France, located in the north-central pa...\n", "  3. [分数: 59.106] London is the capital city of England and the United Kingdom....\n", "\n", "问题 5: Who wrote Romeo and Juliet?\n", "Top-3 匹配文档:\n", "  1. [分数: 73.421] <PERSON> wrote the tragedy Romeo and <PERSON> in the early part of his ...\n", "  2. [分数: 54.250] Einstein developed the theory of relativity in the early 20th century....\n", "  3. [分数: 52.126] <PERSON> invented the telephone in 1876, revolutionizing communicat...\n"]}], "source": ["# 计算相似度矩阵\n", "similarity_scores = dpr_model.compute_similarity(question_embeddings, context_embeddings)\n", "print(f\"相似度矩阵维度: {similarity_scores.shape}\")\n", "print(f\"矩阵含义: {len(questions)} 个问题 × {len(contexts)} 个文档\")\n", "\n", "# 为每个问题找到最相关的文档\n", "print(\"\\n=== 检索结果 ===\")\n", "for i, question in enumerate(questions):\n", "    scores = similarity_scores[i]\n", "    \n", "    # 获取top-3最相关的文档\n", "    top_k = 3\n", "    top_indices = torch.topk(scores, k=top_k).indices\n", "    top_scores = torch.topk(scores, k=top_k).values\n", "    \n", "    print(f\"\\n问题 {i+1}: {question}\")\n", "    print(\"Top-3 匹配文档:\")\n", "    \n", "    for rank, (idx, score) in enumerate(zip(top_indices, top_scores)):\n", "        print(f\"  {rank+1}. [分数: {score:.3f}] {contexts[idx][:80]}...\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. 可视化相似度矩阵\n", "\n", "通过热力图可视化问题和文档之间的相似度关系。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1200x800 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "最高相似度: 91.2631\n", "问题: When was Python programming language created?\n", "文档: Python was created by <PERSON> and first released in 1991 as a high-level programming language.\n"]}], "source": ["\n", "# 设置中文字体\n", "plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']  # 用来正常显示中文标签\n", "plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号\n", "\n", "# 可视化相似度矩阵\n", "plt.figure(figsize=(12, 8))\n", "similarity_matrix = similarity_scores.numpy()\n", "\n", "# 创建热力图\n", "im = plt.imshow(similarity_matrix, cmap='Blues', aspect='auto')\n", "plt.colorbar(im, label='相似度分数')\n", "\n", "# 设置标签\n", "plt.xlabel('文档索引')\n", "plt.ylabel('问题索引')\n", "plt.title('DPR 问题-文档相似度矩阵')\n", "\n", "# 添加数值标注\n", "for i in range(len(questions)):\n", "    for j in range(len(contexts)):\n", "        plt.text(j, i, f'{similarity_matrix[i, j]:.2f}', \n", "                ha='center', va='center', fontsize=8)\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# 显示最高相似度的问题-文档对\n", "max_similarity = torch.max(similarity_scores)\n", "max_pos = torch.argmax(similarity_scores)\n", "max_q_idx = max_pos // len(contexts)\n", "max_c_idx = max_pos % len(contexts)\n", "\n", "print(f\"\\n最高相似度: {max_similarity:.4f}\")\n", "print(f\"问题: {questions[max_q_idx]}\")\n", "print(f\"文档: {contexts[max_c_idx]}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. DPR训练过程演示\n", "\n", "演示DPR的训练过程，特别是in-batch negative sampling的概念。"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== DPR训练过程演示 ===\n", "批次大小: 4\n", "问题嵌入: torch.<PERSON><PERSON>([4, 768])\n", "文档嵌入: torch.<PERSON><PERSON>([4, 768])\n", "\n", "相似度矩阵:\n", "[[-0.05751814  0.05997592 -0.03524333  0.01844726]\n", " [ 0.00861109  0.00401761  0.0266504   0.06639262]\n", " [ 0.01500174 -0.00720881 -0.02278805  0.01820277]\n", " [ 0.04108658 -0.01974455 -0.05274502 -0.02554977]]\n", "\n", "正确标签: [0 1 2 3]\n", "模型预测: [1 3 3 0]\n", "训练损失: 1.4146\n", "批次准确率: 0.00\n"]}], "source": ["class DPRTrainingDemo:\n", "    \"\"\"DPR训练过程演示\"\"\"\n", "    \n", "    def __init__(self, hidden_size=768):\n", "        self.hidden_size = hidden_size\n", "    \n", "    def simulate_training_batch(self, batch_size=4):\n", "        \"\"\"模拟一个训练批次\"\"\"\n", "        # 模拟问题和正例文档的嵌入\n", "        # 实际训练中这些由编码器生成\n", "        question_emb = torch.randn(batch_size, self.hidden_size)\n", "        positive_doc_emb = torch.randn(batch_size, self.hidden_size)\n", "        \n", "        # 归一化向量（提高训练稳定性）\n", "        question_emb = F.normalize(question_emb, p=2, dim=1)\n", "        positive_doc_emb = F.normalize(positive_doc_emb, p=2, dim=1)\n", "        \n", "        return question_emb, positive_doc_emb\n", "    \n", "    def compute_dpr_loss(self, question_emb, doc_emb, temperature=1.0):\n", "        \"\"\"计算DPR损失函数\"\"\"\n", "        batch_size = question_emb.shape[0]\n", "        \n", "        # 计算相似度矩阵\n", "        similarity_matrix = torch.matmul(question_emb, doc_emb.T) / temperature\n", "        \n", "        # 标签：对角线为正例（每个问题对应自己的文档）\n", "        labels = torch.arange(batch_size)\n", "        \n", "        # 交叉熵损失\n", "        loss = F.cross_entropy(similarity_matrix, labels)\n", "        \n", "        return loss, similarity_matrix, labels\n", "    \n", "    def demonstrate_training(self):\n", "        \"\"\"演示训练过程\"\"\"\n", "        print(\"=== DPR训练过程演示 ===\")\n", "        \n", "        batch_size = 4\n", "        question_emb, doc_emb = self.simulate_training_batch(batch_size)\n", "        \n", "        print(f\"批次大小: {batch_size}\")\n", "        print(f\"问题嵌入: {question_emb.shape}\")\n", "        print(f\"文档嵌入: {doc_emb.shape}\")\n", "        \n", "        # 计算损失\n", "        loss, similarity_matrix, labels = self.compute_dpr_loss(question_emb, doc_emb)\n", "        \n", "        print(f\"\\n相似度矩阵:\")\n", "        print(similarity_matrix.detach().numpy())\n", "        \n", "        print(f\"\\n正确标签: {labels.numpy()}\")\n", "        print(f\"模型预测: {torch.argmax(similarity_matrix, dim=1).numpy()}\")\n", "        print(f\"训练损失: {loss.item():.4f}\")\n", "        \n", "        # 计算准确率\n", "        predictions = torch.argmax(similarity_matrix, dim=1)\n", "        accuracy = (predictions == labels).float().mean()\n", "        print(f\"批次准确率: {accuracy:.2f}\")\n", "        \n", "        return loss, accuracy\n", "\n", "# 运行训练演示\n", "trainer = DPRTrainingDemo()\n", "loss, accuracy = trainer.demonstrate_training()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. In-batch Negative Sampling 详解\n", "\n", "这是DPR训练的核心技巧，大大提高了训练效率。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def explain_negative_sampling():\n", "    \"\"\"解释in-batch negative sampling\"\"\"\n", "    \n", "    print(\"=== In-batch Negative Sampling 详解 ===\")\n", "    print()\n", "    \n", "    # 模拟一个批次的训练数据\n", "    batch_questions = [\n", "        \"What is the capital of France?\",\n", "        \"Who invented the telephone?\", \n", "        \"When was Python created?\",\n", "        \"What is the largest planet?\"\n", "    ]\n", "    \n", "    batch_positive_docs = [\n", "        \"Paris is the capital of France.\",\n", "        \"<PERSON> invented the telephone.\",\n", "        \"Python was created by <PERSON> in 1991.\",\n", "        \"Jupiter is the largest planet in our solar system.\"\n", "    ]\n", "    \n", "    print(\"训练批次示例:\")\n", "    for i, (q, d) in enumerate(zip(batch_questions, batch_positive_docs)):\n", "        print(f\"样本 {i}: Q='{q}' → D='{d}'\")\n", "    \n", "    print(\"\\nIn-batch Negative Sampling 策略:\")\n", "    print(\"对于每个问题，其负例文档是批次中其他问题的正例文档\")\n", "    print()\n", "    \n", "    for i, question in enumerate(batch_questions):\n", "        print(f\"问题 {i}: '{question}'\")\n", "        print(f\"  正例: '{batch_positive_docs[i]}'\")\n", "        print(\"  负例:\")\n", "        for j, neg_doc in enumerate(batch_positive_docs):\n", "            if i != j:\n", "                print(f\"    - '{neg_doc}'\")\n", "        print()\n", "    \n", "    print(\"优势:\")\n", "    print(\"1. 无需额外采样负例，提高训练效率\")\n", "    print(\"2. 负例质量较高（都是真实的正例文档）\")\n", "    print(\"3. 批次越大，负例越多，训练效果越好\")\n", "\n", "explain_negative_sampling()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 9. 与项目中实现的对比\n", "\n", "让我们看看这个简化版本与项目中`RetrieverDPR`的区别。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"=== 简化版 vs 项目实现对比 ===\")\n", "print()\n", "\n", "comparison = {\n", "    \"特性\": [\"模型架构\", \"训练策略\", \"负采样\", \"损失函数\", \"评估指标\", \"分布式训练\", \"表格处理\"],\n", "    \"简化版\": [\n", "        \"基础双编码器\",\n", "        \"简单演示\", \n", "        \"In-batch sampling\",\n", "        \"交叉熵损失\",\n", "        \"准确率\",\n", "        \"不支持\",\n", "        \"不支持\"\n", "    ],\n", "    \"项目实现\": [\n", "        \"可配置编码器\",\n", "        \"完整训练流程\",\n", "        \"多种负采样策略\", \n", "        \"多种损失函数\",\n", "        \"Recall@K等\",\n", "        \"支持多GPU\",\n", "        \"专门的表格编码\"\n", "    ]\n", "}\n", "\n", "import pandas as pd\n", "df = pd.DataFrame(comparison)\n", "print(df.to_string(index=False))\n", "\n", "print(\"\\n项目中的高级特性:\")\n", "print(\"1. 支持表格数据的特殊编码（TextBasedTableInput）\")\n", "print(\"2. 多种负采样策略（BM25, 随机采样等）\")\n", "print(\"3. 分布式训练支持\")\n", "print(\"4. 与下游任务的端到端训练\")\n", "print(\"5. 详细的评估指标（Recall@1, @5, @10等）\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 10. 总结\n", "\n", "通过这个教程，你应该理解了：\n", "\n", "1. **DPR的核心思想**：使用稠密向量表示问题和文档\n", "2. **双编码器架构**：分别编码问题和文档\n", "3. **相似度计算**：通过点积计算匹配度\n", "4. **训练策略**：in-batch negative sampling\n", "5. **实际应用**：在表格问答中的使用\n", "\n", "DPR是现代检索系统的重要基础，在表格问答、开放域问答等任务中都有广泛应用。"]}], "metadata": {"kernelspec": {"display_name": "tableqa", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.12"}}, "nbformat": 4, "nbformat_minor": 4}