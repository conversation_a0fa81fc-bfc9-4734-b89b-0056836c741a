!curl https://huggingface.co/

import torch
import numpy as np
from transformers import (
    DPRQuestionEncoder, DPRQuestionEncoderTokenizer,
    BertTokenizer, BertModel
)
from transformers import BertTokenizer, BertModel

# 示例文本
text = "My name is <PERSON><PERSON><PERSON> james"
print(f"原始文本: '{text}'")
print("=" * 50)

# 直接从dir目录加载（不需要子目录）
model_path = "/Users/<USER>/Documents/Code/Experiment/robust-tableqa-main/dir"

# 加载分词器和模型
tokenizer = DPRQuestionEncoderTokenizer.from_pretrained(model_path)
model = DPRQuestionEncoder.from_pretrained(model_path)

print("🔧 分词器的作用：文本 → 数字")
# 步骤1: 分词的作用就是将文本转换为数字，只是将其单纯的将文本转换为数字，并没有学习到语义  
tokens = tokenizer.tokenize(text)
print(f"1. 分词结果: {tokens}")

# 步骤2: 转换为ID
token_ids = tokenizer.convert_tokens_to_ids(tokens)
print(f"2. Token IDs: {token_ids}")

# 步骤3: 添加特殊token并padding
encoded = tokenizer(
    text,
    padding=True,
    truncation=True,
    max_length=512,
    return_tensors="pt"
)

print(f"3. 最终输入张量:")
print(f"   input_ids shape: {encoded['input_ids'].shape}")
print(f"   input_ids: {encoded['input_ids']}")
print(f"   attention_mask: {encoded['attention_mask']}")

print("\n📝 分词器总结:")
print("- 输入: 原始文本字符串")
print("- 输出: 数字张量 (token IDs)")
print("- 作用: 将人类语言转换为模型能理解的数字")

model_path = "/Users/<USER>/Documents/Code/Experiment/robust-tableqa-main/dir"
# 加载编码器，又称之为语义理解器。
encoder = DPRQuestionEncoder.from_pretrained(
    model_path
)

print("🧠 编码器的作用：数字 → 语义向量")
print()

# 使用编码器处理分词器的输出
with torch.no_grad():
    outputs = encoder(**encoded)
    embeddings = outputs.pooler_output

print(f"编码器输入: {encoded['input_ids'].shape} (来自分词器)")
print(f"编码器输出: {embeddings.shape} (语义向量)")
print(f"向量前5个值: {embeddings[0][:5].numpy()}")

print("\n🧠 编码器总结:")
print("- 输入: 数字张量 (来自分词器)")
print("- 输出: 高维语义向量 (768维)")
print("- 作用: 理解文本语义，生成可比较的向量表示")

def demonstrate_pipeline(text1, text2):
    """演示完整的处理流水线"""
    print(f"🔄 处理流水线演示")
    print(f"文本1: '{text1}'")
    print(f"文本2: '{text2}'")
    print()
    
    # 步骤1: 分词器处理
    print("步骤1: 分词器处理")
    inputs1 = tokenizer(text1, return_tensors="pt", padding=True, truncation=True)
    inputs2 = tokenizer(text2, return_tensors="pt", padding=True, truncation=True)
    
    print(f"  文本1 → token_ids: {inputs1['input_ids'][0][:10]}...")
    print(f"  文本2 → token_ids: {inputs2['input_ids'][0][:10]}...")
    
    # 步骤2: 编码器处理
    print("\n步骤2: 编码器处理")
    with torch.no_grad():
        emb1 = encoder(**inputs1).pooler_output
        emb2 = encoder(**inputs2).pooler_output
    
    print(f"  文本1 → 向量: shape={emb1.shape}, 前3值={emb1[0][:3].numpy()}")
    print(f"  文本2 → 向量: shape={emb2.shape}, 前3值={emb2[0][:3].numpy()}")
    
    # 步骤3: 语义相似度计算
    print("\n步骤3: 语义相似度计算")
    similarity = torch.cosine_similarity(emb1, emb2)
    print(f"  相似度: {similarity.item():.4f}")
    
    return similarity.item()

# 测试相似文本
sim1 = demonstrate_pipeline(
    "什么是深度学习？", 
    "深度学习是什么？"
)

print("\n" + "="*50)

# 测试不相似文本
sim2 = demonstrate_pipeline(
    "什么是深度学习？", 
    "今天天气很好"
)

print(f"\n📊 结果分析:")
print(f"相似文本相似度: {sim1:.4f} (高)")
print(f"不同文本相似度: {sim2:.4f} (低)")
print("这证明编码器能理解语义！")

print("🎯 分词器 vs 编码器 - 关键区别")
print("=" * 60)

comparison = {
    "特征": ["输入", "输出", "维度", "作用", "可训练", "计算复杂度"],
    "分词器 (Tokenizer)": [
        "文本字符串",
        "整数ID序列", 
        "[seq_len]",
        "文本预处理",
        "否 (规则/词表)",
        "低 (查表操作)"
    ],
    "编码器 (Encoder)": [
        "整数ID序列",
        "语义向量",
        "[batch, 768]", 
        "语义理解",
        "是 (神经网络)",
        "高 (深度网络)"
    ]
}

for i, feature in enumerate(comparison["特征"]):
    print(f"{feature:12} | {comparison['分词器 (Tokenizer)'][i]:20} | {comparison['编码器 (Encoder)'][i]}")

print("\n🔗 它们的关系:")
print("文本 → [分词器] → Token IDs → [编码器] → 语义向量")
print("     (预处理)              (理解语义)")

print("\n💡 类比理解:")
print("分词器 = 翻译官 (将中文翻译成数字)")
print("编码器 = 理解者 (理解数字背后的含义)")

class DPRExample:
    """展示分词器和编码器如何配合工作"""
    model_path = "/Users/<USER>/Documents/Code/Experiment/robust-tableqa-main/dir"
    def __init__(self):
        # 分词器：负责文本预处理,分词器的作用就是将文本转换为数字，并没有学习到语义
        self.tokenizer = DPRQuestionEncoderTokenizer.from_pretrained(
            self.model_path
        )
        
        # 编码器：负责语义理解，将数字转换为语义向量
        self.encoder = DPRQuestionEncoder.from_pretrained(
            self.model_path
        )
    
    def process_text(self, text):
        """完整的文本处理流程"""
        print(f"📝 处理文本: '{text}'")
        
        # 阶段1: 分词器工作
        print("\n🔧 阶段1: 分词器工作")
        tokens = self.tokenizer.tokenize(text)
        print(f"  分词: {tokens}")
        
        inputs = self.tokenizer(
            text, 
            return_tensors="pt", 
            padding=True, 
            truncation=True
        )
        print(f"  转换为IDs: {inputs['input_ids'][0][:10]}...")
        
        # 阶段2: 编码器工作
        print("\n🧠 阶段2: 编码器工作")
        with torch.no_grad():
            outputs = self.encoder(**inputs)
            embeddings = outputs.pooler_output
        
        print(f"  生成语义向量: {embeddings.shape}")
        print(f"  向量范数: {torch.norm(embeddings).item():.4f}")
        
        return embeddings

# 演示
dpr_demo = DPRExample()
embedding = dpr_demo.process_text("深度学习的应用有哪些？")

print("\n✅ 总结:")
print("- 分词器：将文本转换为模型能处理的数字格式")
print("- 编码器：理解这些数字的语义含义，生成有意义的向量")
print("- 两者缺一不可，分工明确！")