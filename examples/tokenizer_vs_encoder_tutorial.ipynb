{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 分词器 vs 编码器：深入理解区别\n", "## 它们在NLP流水线中的不同作用"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<!doctype html>\n", "<html class=\"\">\n", "\t<head>\n", "\t\t<meta charset=\"utf-8\" />\n", "\t\t<meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, user-scalable=no\" />\n", "\t\t<meta name=\"description\" content=\"We’re on a journey to advance and democratize artificial intelligence through open source and open science.\" />\n", "\t\t<meta property=\"fb:app_id\" content=\"1321688464574422\" />\n", "\t\t<meta name=\"twitter:card\" content=\"summary_large_image\" />\n", "\t\t<meta name=\"twitter:site\" content=\"@huggingface\" />\n", "\t\t<meta name=\"twitter:image\" content=\"https://huggingface.co/front/thumbnails/v2-2.png\" />\n", "\t\t<meta property=\"og:title\" content=\"Hugging Face – The AI community building the future.\" />\n", "\t\t<meta property=\"og:type\" content=\"website\" />\n", "\t\t<meta property=\"og:url\" content=\"https://huggingface.co/\" />\n", "\t\t<meta property=\"og:image\" content=\"https://huggingface.co/front/thumbnails/v2-2.png\" />\n", "\n", "\t\t<link rel=\"stylesheet\" href=\"/front/build/kube-37f3ff5/style.css\" />\n", "\n", "\t\t<link rel=\"preconnect\" href=\"https://fonts.gstatic.com\" />\n", "\t\t<link\n", "\t\t\thref=\"https://fonts.googleapis.com/css2?family=Source+Sans+Pro:ital,wght@0,200;0,300;0,400;0,600;0,700;1,200;1,300;1,400;1,600;1,700&display=swap\"\n", "\t\t\trel=\"stylesheet\"\n", "\t\t/>\n", "\t\t<link\n", "\t\t\thref=\"https://fonts.googleapis.com/css2?family=IBM+Plex+Mono:wght@400;600;700&display=swap\"\n", "\t\t\trel=\"stylesheet\"\n", "\t\t/>\n", "\n", "\t\t<link\n", "\t\t\trel=\"preload\"\n", "\t\t\thref=\"https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.12.0/katex.min.css\"\n", "\t\t\tas=\"style\"\n", "\t\t\tonload=\"this.onload=null;this.rel='stylesheet'\"\n", "\t\t/>\n", "\t\t<noscript>\n", "\t\t\t<link rel=\"stylesheet\" href=\"https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.12.0/katex.min.css\" />\n", "\t\t</noscript>\n", "\n", "\t\t<script>const guestTheme = document.cookie.match(/theme=(\\w+)/)?.[1]; document.documentElement.classList.toggle('dark', guestTheme === 'dark' || ( (!guestTheme || guestTheme === 'system') && window.matchMedia('(prefers-color-scheme: dark)').matches));</script>\n", "<link rel=\"canonical\" href=\"https://huggingface.co/\">  <!-- HEAD_svelte-vwinwk_START --><link rel=\"alternate\" type=\"application/rss+xml\" href=\"/blog/feed.xml\" title=\"Hugging Face Blog\"><!-- HEAD_svelte-vwinwk_END -->\n", "\n", "\t\t<title>Hugging Face – The AI community building the future.</title>\n", "\n", "\t\t<script\n", "\t\t\tdefer\n", "\t\t\tdata-domain=\"huggingface.co\"\n", "\t\t\tevent-loggedIn=\"false\"\n", "\t\t\tsrc=\"/js/script.pageview-props.js\"\n", "\t\t></script>\n", "\t\t<script>\n", "\t\t\twindow.plausible =\n", "\t\t\t\twindow.plausible ||\n", "\t\t\t\tfunction () {\n", "\t\t\t\t\t(window.plausible.q = window.plausible.q || []).push(arguments);\n", "\t\t\t\t};\n", "\t\t</script>\n", "\t\t<script>\n", "\t\t\twindow.hubConfig = {\"features\":{\"signupDisabled\":false},\"sshGitUrl\":\"*********\",\"moonHttpUrl\":\"https:\\/\\/huggingface.co\",\"captchaApiKey\":\"bd5f2066-93dc-4bdd-a64b-a24646ca3859\",\"captchaDisabledOnSignup\":true,\"datasetViewerPublicUrl\":\"https:\\/\\/datasets-server.huggingface.co\",\"stripePublicKey\":\"pk_live_x2tdjFXBCvXo2FFmMybezpeM00J6gPCAAc\",\"environment\":\"production\",\"userAgent\":\"HuggingFace (production)\",\"spacesIframeDomain\":\"hf.space\",\"spacesApiUrl\":\"https:\\/\\/api.hf.space\",\"docSearchKey\":\"ece5e02e57300e17d152c08056145326e90c4bff3dd07d7d1ae40cf1c8d39cb6\",\"logoDev\":{\"apiUrl\":\"https:\\/\\/img.logo.dev\\/\",\"apiKey\":\"pk_UHS2HZOeRnaSOdDp7jbd5w\"}};\n", "\t\t</script>\n", "\t\t<script type=\"text/javascript\" src=\"https://de5282c3ca0c.edge.sdk.awswaf.com/de5282c3ca0c/526cf06acb0d/challenge.js\" defer></script> \n", "\t</head>\n", "\t<body class=\"flex flex-col min-h-dvh bg-white dark:bg-gray-950 text-black HomePage\">\n", "\t\t\n", "\n", "<div class=\"flex min-h-dvh flex-col\"><div class=\"SVELTE_HYDRATER contents\" data-target=\"SystemThemeMonitor\" data-props=\"{&quot;isLoggedIn&quot;:false}\"></div>\n", "\n", "\t<div class=\"SVELTE_HYDRATER contents\" data-target=\"MainHeader\" data-props=\"{&quot;classNames&quot;:&quot;border-transparent!&quot;,&quot;isWide&quot;:false,&quot;isZh&quot;:false,&quot;isPro&quot;:false}\"><header class=\"border-b border-gray-100 border-transparent!\"><div class=\"w-full px-4 container flex h-16 items-center\"><div class=\"flex flex-1 items-center\"><a class=\"mr-5 flex flex-none items-center lg:mr-6\" href=\"/\"><img alt=\"Hugging Face's logo\" class=\"w-7 md:mr-2\" src=\"/front/assets/huggingface_logo-noborder.svg\">\n", "\t\t\t\t<span class=\"hidden whitespace-nowrap text-lg font-bold md:block\">Hugging Face</span></a>\n", "\t\t\t<div class=\"relative flex-1 lg:max-w-sm mr-2 sm:mr-4 md:mr-3 xl:mr-6\"><input autocomplete=\"off\" class=\"w-full dark:bg-gray-950 pl-8 form-input-alt h-9 pr-3 focus:shadow-xl \" name=\"\" placeholder=\"Search models, datasets, users...\"   spellcheck=\"false\" type=\"text\" value=\"\">\n", "\t<svg class=\"absolute left-2.5 text-gray-400 top-1/2 transform -translate-y-1/2\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" aria-hidden=\"true\" focusable=\"false\" role=\"img\" width=\"1em\" height=\"1em\" preserveAspectRatio=\"xMidYMid meet\" viewBox=\"0 0 32 32\"><path d=\"M30 28.59L22.45 21A11 11 0 1 0 21 22.45L28.59 30zM5 14a9 9 0 1 1 9 9a9 9 0 0 1-9-9z\" fill=\"currentColor\"></path></svg>\n", "\t</div>\n", "\t\t\t<div class=\"flex flex-none items-center justify-center p-0.5 place-self-stretch lg:hidden\"><button class=\"relative z-40 flex h-6 w-8 items-center justify-center\" type=\"button\"><svg width=\"1em\" height=\"1em\" viewBox=\"0 0 10 10\" class=\"text-xl\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" aria-hidden=\"true\" focusable=\"false\" role=\"img\" preserveAspectRatio=\"xMidYMid meet\" fill=\"currentColor\"><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M1.65039 2.9999C1.65039 2.8066 1.80709 2.6499 2.00039 2.6499H8.00039C8.19369 2.6499 8.35039 2.8066 8.35039 2.9999C8.35039 3.1932 8.19369 3.3499 8.00039 3.3499H2.00039C1.80709 3.3499 1.65039 3.1932 1.65039 2.9999ZM1.65039 4.9999C1.65039 4.8066 1.80709 4.6499 2.00039 4.6499H8.00039C8.19369 4.6499 8.35039 4.8066 8.35039 4.9999C8.35039 5.1932 8.19369 5.3499 8.00039 5.3499H2.00039C1.80709 5.3499 1.65039 5.1932 1.65039 4.9999ZM2.00039 6.6499C1.80709 6.6499 1.65039 6.8066 1.65039 6.9999C1.65039 7.1932 1.80709 7.3499 2.00039 7.3499H8.00039C8.19369 7.3499 8.35039 7.1932 8.35039 6.9999C8.35039 6.8066 8.19369 6.6499 8.00039 6.6499H2.00039Z\"></path></svg>\n", "\t\t</button>\n", "\n", "\t</div></div>\n", "\t\t<nav aria-label=\"Main\" class=\"ml-auto hidden lg:block\"><ul class=\"flex items-center gap-x-1 2xl:gap-x-2\"><li class=\"hover:text-indigo-700\"><a class=\"group flex items-center px-2 py-0.5 dark:text-gray-300 dark:hover:text-gray-100\" href=\"/models\"><svg class=\"mr-1.5 text-gray-400 group-hover:text-indigo-500\" style=\"\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" aria-hidden=\"true\" focusable=\"false\" role=\"img\" width=\"1em\" height=\"1em\" preserveAspectRatio=\"xMidYMid meet\" viewBox=\"0 0 24 24\"><path class=\"uim-quaternary\" d=\"M20.23 7.24L12 12L3.77 7.24a1.98 1.98 0 0 1 .7-.71L11 2.76c.62-.35 1.38-.35 2 0l6.53 3.77c.29.173.531.418.7.71z\" opacity=\".25\" fill=\"currentColor\"></path><path class=\"uim-tertiary\" d=\"M12 12v9.5a2.09 2.09 0 0 1-.91-.21L4.5 17.48a2.003 2.003 0 0 1-1-1.73v-7.5a2.06 2.06 0 0 1 .27-1.01L12 12z\" opacity=\".5\" fill=\"currentColor\"></path><path class=\"uim-primary\" d=\"M20.5 8.25v7.5a2.003 2.003 0 0 1-1 1.73l-6.62 3.82c-.275.13-.576.198-.88.2V12l8.23-4.76c.175.308.268.656.27 1.01z\" fill=\"currentColor\"></path></svg>\n", "\t\t\t\t\t\tModels</a>\n", "\t\t\t\t</li><li class=\"hover:text-red-700\"><a class=\"group flex items-center px-2 py-0.5 dark:text-gray-300 dark:hover:text-gray-100\" href=\"/datasets\"><svg class=\"mr-1.5 text-gray-400 group-hover:text-red-500\" style=\"\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" aria-hidden=\"true\" focusable=\"false\" role=\"img\" width=\"1em\" height=\"1em\" preserveAspectRatio=\"xMidYMid meet\" viewBox=\"0 0 25 25\"><ellipse cx=\"12.5\" cy=\"5\" fill=\"currentColor\" fill-opacity=\"0.25\" rx=\"7.5\" ry=\"2\"></ellipse><path d=\"M12.5 15C16.6421 15 20 14.1046 20 13V20C20 21.1046 16.6421 22 12.5 22C8.35786 22 5 21.1046 5 20V13C5 14.1046 8.35786 15 12.5 15Z\" fill=\"currentColor\" opacity=\"0.5\"></path><path d=\"M12.5 7C16.6421 7 20 6.10457 20 5V11.5C20 12.6046 16.6421 13.5 12.5 13.5C8.35786 13.5 5 12.6046 5 11.5V5C5 6.10457 8.35786 7 12.5 7Z\" fill=\"currentColor\" opacity=\"0.5\"></path><path d=\"M5.23628 12C5.08204 12.1598 5 12.8273 5 13C5 14.1046 8.35786 15 12.5 15C16.6421 15 20 14.1046 20 13C20 12.8273 19.918 12.1598 19.7637 12C18.9311 12.8626 15.9947 13.5 12.5 13.5C9.0053 13.5 6.06886 12.8626 5.23628 12Z\" fill=\"currentColor\"></path></svg>\n", "\t\t\t\t\t\tDatasets</a>\n", "\t\t\t\t</li><li class=\"hover:text-blue-700\"><a class=\"group flex items-center px-2 py-0.5 dark:text-gray-300 dark:hover:text-gray-100\" href=\"/spaces\"><svg class=\"mr-1.5 text-gray-400 group-hover:text-blue-500\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" aria-hidden=\"true\" focusable=\"false\" role=\"img\" width=\"1em\" height=\"1em\" viewBox=\"0 0 25 25\"><path opacity=\".5\" d=\"M6.016 14.674v4.31h4.31v-4.31h-4.31ZM14.674 14.674v4.31h4.31v-4.31h-4.31ZM6.016 6.016v4.31h4.31v-4.31h-4.31Z\" fill=\"currentColor\"></path><path opacity=\".75\" fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M3 4.914C3 3.857 3.857 3 4.914 3h6.514c.884 0 1.628.6 1.848 1.414a5.171 5.171 0 0 1 7.31 7.31c.815.22 1.414.964 1.414 1.848v6.514A1.914 1.914 0 0 1 20.086 22H4.914A1.914 1.914 0 0 1 3 20.086V4.914Zm3.016 1.102v4.31h4.31v-4.31h-4.31Zm0 12.968v-4.31h4.31v4.31h-4.31Zm8.658 0v-4.31h4.31v4.31h-4.31Zm0-10.813a2.155 2.155 0 1 1 4.31 0 2.155 2.155 0 0 1-4.31 0Z\" fill=\"currentColor\"></path><path opacity=\".25\" d=\"M16.829 6.016a2.155 2.155 0 1 0 0 4.31 2.155 2.155 0 0 0 0-4.31Z\" fill=\"currentColor\"></path></svg>\n", "\t\t\t\t\t\tSpaces</a>\n", "\t\t\t\t</li><li class=\"max-xl:hidden relative\"><div class=\"relative \">\n", "\t<button class=\"group flex items-center px-2 py-0.5 dark:text-gray-300 hover:text-yellow-700 dark:hover:text-gray-100 \" type=\"button\">\n", "\t\t<svg class=\"mr-1.5 mr-1.5 text-gray-400 text-yellow-500! group-hover:text-yellow-500\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" aria-hidden=\"true\" focusable=\"false\" role=\"img\" width=\"1em\" height=\"1em\" preserveAspectRatio=\"xMidYMid meet\" viewBox=\"0 0 32 32\"><path d=\"M20.6081 3C21.7684 3 22.8053 3.49196 23.5284 4.38415C23.9756 4.93678 24.4428 5.82749 24.4808 7.16133C24.9674 7.01707 25.4353 6.93643 25.8725 6.93643C26.9833 6.93643 27.9865 7.37587 28.696 8.17411C29.6075 9.19872 30.0124 10.4579 29.8361 11.7177C29.7523 12.3177 29.5581 12.8555 29.2678 13.3534C29.8798 13.8646 30.3306 14.5763 30.5485 15.4322C30.719 16.1032 30.8939 17.5006 29.9808 18.9403C30.0389 19.0342 30.0934 19.1319 30.1442 19.2318C30.6932 20.3074 30.7283 21.5229 30.2439 22.6548C29.5093 24.3704 27.6841 25.7219 24.1397 27.1727C21.9347 28.0753 19.9174 28.6523 19.8994 28.6575C16.9842 29.4379 14.3477 29.8345 12.0653 29.8345C7.87017 29.8345 4.8668 28.508 3.13831 25.8921C0.356375 21.6797 0.754104 17.8269 4.35369 14.1131C6.34591 12.058 7.67023 9.02782 7.94613 8.36275C8.50224 6.39343 9.97271 4.20438 12.4172 4.20438H12.4179C12.6236 4.20438 12.8314 4.2214 13.0364 4.25468C14.107 4.42854 15.0428 5.06476 15.7115 6.02205C16.4331 5.09583 17.134 4.359 17.7682 3.94323C18.7242 3.31737 19.6794 3 20.6081 3ZM20.6081 5.95917C20.2427 5.95917 19.7963 6.1197 19.3039 6.44225C17.7754 7.44319 14.8258 12.6772 13.7458 14.7131C13.3839 15.3952 12.7655 15.6837 12.2086 15.6837C11.1036 15.6837 10.2408 14.5497 12.1076 13.1085C14.9146 10.9402 13.9299 7.39584 12.5898 7.1776C12.5311 7.16799 12.4731 7.16355 12.4172 7.16355C11.1989 7.16355 10.6615 9.33114 10.6615 9.33114C10.6615 9.33114 9.0863 13.4148 6.38031 16.206C3.67434 18.998 3.5346 21.2388 5.50675 24.2246C6.85185 26.2606 9.42666 26.8753 12.0653 26.8753C14.8021 26.8753 17.6077 26.2139 19.1799 25.793C19.2574 25.7723 28.8193 22.984 27.6081 20.6107C27.4046 20.212 27.0693 20.0522 26.6471 20.0522C24.9416 20.0522 21.8393 22.6726 20.5057 22.6726C20.2076 22.6726 19.9976 22.5416 19.9116 22.222C19.3433 20.1173 28.552 19.2325 27.7758 16.1839C27.639 15.6445 27.2677 15.4256 26.746 15.4263C24.4923 15.4263 19.4358 19.5181 18.3759 19.5181C18.2949 19.5181 18.2368 19.4937 18.2053 19.4419C17.6743 18.557 17.9653 17.9394 21.7082 15.6009C25.4511 13.2617 28.0783 11.8545 26.5841 10.1752C26.4121 9.98141 26.1684 9.8956 25.8725 9.8956C23.6001 9.89634 18.2311 14.9403 18.2311 14.9403C18.2311 14.9403 16.7821 16.496 15.9057 16.496C15.7043 16.496 15.533 16.4139 15.4169 16.2112C14.7956 15.1296 21.1879 10.1286 21.5484 8.06535C21.7928 6.66715 21.3771 5.95917 20.6081 5.95917Z\" fill=\"#FF9D00\"></path><path d=\"M5.50686 24.2246C3.53472 21.2387 3.67446 18.9979 6.38043 16.206C9.08641 13.4147 10.6615 9.33111 10.6615 9.33111C10.6615 9.33111 11.2499 6.95933 12.59 7.17757C13.93 7.39581 14.9139 10.9401 12.1069 13.1084C9.29997 15.276 12.6659 16.7489 13.7459 14.713C14.8258 12.6772 17.7747 7.44316 19.304 6.44221C20.8326 5.44128 21.9089 6.00204 21.5484 8.06532C21.188 10.1286 14.795 15.1295 15.4171 16.2118C16.0391 17.2934 18.2312 14.9402 18.2312 14.9402C18.2312 14.9402 25.0907 8.49588 26.5842 10.1752C28.0776 11.8545 25.4512 13.2616 21.7082 15.6008C17.9646 17.9393 17.6744 18.557 18.2054 19.4418C18.7372 20.3266 26.9998 13.1351 27.7759 16.1838C28.5513 19.2324 19.3434 20.1173 19.9117 22.2219C20.48 24.3274 26.3979 18.2382 27.6082 20.6107C28.8193 22.9839 19.2574 25.7722 19.18 25.7929C16.0914 26.62 8.24723 28.3726 5.50686 24.2246Z\" fill=\"#FFD21E\"></path></svg>\n", "\t\t\tCommunity\n", "\t\t</button>\n", "\t\n", "\t\n", "\t</div>\n", "\t\t\t\t</li><li class=\"hover:text-yellow-700\"><a class=\"group flex items-center px-2 py-0.5 dark:text-gray-300 dark:hover:text-gray-100\" href=\"/docs\"><svg xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" aria-hidden=\"true\" role=\"img\" class=\"mr-1.5 text-gray-400 group-hover:text-yellow-500\" width=\"1em\" height=\"1em\" preserveAspectRatio=\"xMidYMid meet\" viewBox=\"0 0 16 16\"><path d=\"m2.28 3.7-.3.16a.67.67 0 0 0-.34.58v8.73l.***********.***********.***********.***********.***********.***********.08.02h.05l.07.02h.11l.04-.01.07-.02.03-.01.07-.03.22-.12a5.33 5.33 0 0 1 *********.67 0 0 0 .66 0 5.33 5.33 0 0 1 5.33 0 .67.67 0 0 0 1-.58V4.36a.67.67 0 0 0-.34-.5l-.3-.17v7.78a.63.63 0 0 1-.87.59 4.9 4.9 0 0 0-4.35.35l-.65.39a.29.29 0 0 1-.*********** 0 0 1-.16-.04l-.65-.4a4.9 4.9 0 0 0-4.34-.34.63.63 0 0 1-.87-.59V3.7Z\" fill=\"currentColor\" class=\"dark:opacity-40\"></path><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M8 3.1a5.99 5.99 0 0 0-5.3-.43.66.66 0 0 0-.42.62v8.18c0 .***********.59a4.9 4.9 0 0 1 4.34.35l.65.39c.**********.16.04.05 0 .1-.01.15-.04l.65-.4a4.9 4.9 0 0 1 4.35-.34.63.63 0 0 0 .86-.59V3.3a.67.67 0 0 0-.41-.62 5.99 5.99 0 0 0-5.3.43l-.3.17L8 3.1Zm.73 1.87a.43.43 0 1 0-.86 0v5.48a.43.43 0 0 0 .86 0V4.97Z\" fill=\"currentColor\" class=\"opacity-40 dark:opacity-100\"></path><path d=\"M8.73 4.97a.43.43 0 1 0-.86 0v5.48a.43.43 0 1 0 .86 0V4.96Z\" fill=\"currentColor\" class=\"dark:opacity-40\"></path></svg>\n", "\t\t\t\t\t\tDocs</a>\n", "\t\t\t\t</li><li class=\"hover:text-black dark:hover:text-white max-2xl:hidden\"><a class=\"group flex items-center px-2 py-0.5 dark:text-gray-300 dark:hover:text-gray-100\" href=\"/enterprise\"><svg class=\"mr-1.5 text-gray-400 group-hover:text-black dark:group-hover:text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" aria-hidden=\"true\" focusable=\"false\" role=\"img\" width=\"1em\" height=\"1em\" preserveAspectRatio=\"xMidYMid meet\" viewBox=\"0 0 12 12\"><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M4.9 1.35a3.16 3.16 0 0 0-2.8 2.07L.37 8.58C0 9.71.7 10.65 1.86 10.65H7.3a3.2 3.2 0 0 0 2.84-2.07l1.67-5.16c.36-1.13-.3-2.07-1.46-2.07H4.91Zm.4 2.07L3.57 8.47h3.57l.36-1.12H5.4l.28-.91h1.75l.4-1.1H6.07l.3-.83h2l.36-1.1H5.27h.04Z\" fill=\"currentColor\"></path></svg>\n", "\t\t\t\t\t\tEnterprise</a>\n", "\t\t\t\t</li>\n", "\n", "\t\t<li><a class=\"group flex items-center px-2 py-0.5 dark:text-gray-300 dark:hover:text-gray-100\" href=\"/pricing\">Pricing\n", "\t\t\t</a></li>\n", "\n", "\t\t<li><div class=\"relative group\">\n", "\t<button class=\"px-2 py-0.5 hover:text-gray-500 dark:hover:text-gray-600 flex items-center \" type=\"button\">\n", "\t\t<svg class=\" text-gray-500 w-5 group-hover:text-gray-400 dark:text-gray-300 dark:group-hover:text-gray-100\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" aria-hidden=\"true\" focusable=\"false\" role=\"img\" width=\"1em\" height=\"1em\" viewBox=\"0 0 32 18\" preserveAspectRatio=\"xMidYMid meet\"><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M14.4504 3.30221C14.4504 2.836 14.8284 2.45807 15.2946 2.45807H28.4933C28.9595 2.45807 29.3374 2.836 29.3374 3.30221C29.3374 3.76842 28.9595 4.14635 28.4933 4.14635H15.2946C14.8284 4.14635 14.4504 3.76842 14.4504 3.30221Z\" fill=\"currentColor\"></path><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M14.4504 9.00002C14.4504 8.53382 14.8284 8.15588 15.2946 8.15588H28.4933C28.9595 8.15588 29.3374 8.53382 29.3374 9.00002C29.3374 9.46623 28.9595 9.84417 28.4933 9.84417H15.2946C14.8284 9.84417 14.4504 9.46623 14.4504 9.00002Z\" fill=\"currentColor\"></path><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M14.4504 14.6978C14.4504 14.2316 14.8284 13.8537 15.2946 13.8537H28.4933C28.9595 13.8537 29.3374 14.2316 29.3374 14.6978C29.3374 15.164 28.9595 15.542 28.4933 15.542H15.2946C14.8284 15.542 14.4504 15.164 14.4504 14.6978Z\" fill=\"currentColor\"></path><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M1.94549 6.87377C2.27514 6.54411 2.80962 6.54411 3.13928 6.87377L6.23458 9.96907L9.32988 6.87377C9.65954 6.54411 10.194 6.54411 10.5237 6.87377C10.8533 7.20343 10.8533 7.73791 10.5237 8.06756L6.23458 12.3567L1.94549 8.06756C1.61583 7.73791 1.61583 7.20343 1.94549 6.87377Z\" fill=\"currentColor\"></path></svg>\n", "\t\t\t\n", "\t\t</button>\n", "\t\n", "\t\n", "\t</div></li>\n", "\t\t<li><hr class=\"h-5 w-0.5 border-none bg-gray-100 dark:bg-gray-800\"></li>\n", "\t\t<li><a class=\"block cursor-pointer whitespace-nowrap px-2 py-0.5 hover:text-gray-500 dark:text-gray-300 dark:hover:text-gray-100\" href=\"/login\">Log In\n", "\t\t\t\t</a></li>\n", "\t\t\t<li><a class=\"whitespace-nowrap rounded-full border border-transparent bg-gray-900 px-3 py-1 leading-none text-white hover:border-black hover:bg-white hover:text-black\" href=\"/join\">Sign Up\n", "\t\t\t\t\t</a></li></ul></nav></div></header></div>\n", "\t\n", "\t\n", "\t\n", "\t<div class=\"SVELTE_HYDRATER contents\" data-target=\"SSOBanner\" data-props=\"{}\"></div>\n", "\t\n", "\n", "\n", "\n", "\t<main class=\"flex flex-1 flex-col\"><div class=\"container pt-2 md:pt-14 2xl:pt-20\"><div class=\"relative flex flex-col justify-center rounded-[25px] bg-gray-950 dark:bg-gray-900/60 max-lg:items-center max-lg:overflow-hidden max-lg:pt-14 max-lg:text-center max-lg:has-[.bannerEl]:pt-20 lg:h-[620px] lg:p-16 xl:p-20 2xl:h-[720px]\"><div class=\"SVELTE_HYDRATER contents\" data-target=\"ProductBanners\" data-props=\"{&quot;classNames&quot;:&quot;absolute top-4 md:top-6 lg:left-6 overflow-hidden max-sm:inset-x-4 bannerEl&quot;,&quot;productBanners&quot;:[]}\"></div>\n", "\n", "\t\t\t<img src=\"/front/assets/huggingface_logo-noborder.svg\" class=\"-mt-3 mb-7 w-20\" alt=\"\">\n", "\n", "\t\t\t<h1 class=\"relative mb-8 max-w-lg text-balance text-4xl font-bold text-white lg:mb-10 lg:text-5xl 2xl:max-w-xl 2xl:text-6xl\">The AI community building the future.\n", "\t\t\t</h1>\n", "\t\t\t<p class=\"max-w-lg text-balance text-gray-300/80 max-lg:mb-9 max-lg:px-6 lg:pr-6 lg:text-lg\">The platform where the machine learning community collaborates on models, datasets, and applications.\n", "\t\t\t</p>\n", "\t\t\t<div class=\"text-white! flex items-center gap-2.5 whitespace-nowrap text-sm max-lg:mb-12 lg:mt-11 lg:gap-3.5 lg:text-base 2xl:text-lg\"><div class=\"group relative h-[40px] lg:h-[42px] 2xl:h-[45px]\"><a href=\"/spaces\" class=\"bg-linear-to-r group relative z-10 flex h-[40px] items-center rounded-full border border-gray-600 from-transparent via-white/5 to-transparent px-[1.35rem] leading-none shadow-xl hover:via-white/10 hover:shadow-none dark:border-gray-600 lg:h-[42px] 2xl:h-[45px] 2xl:px-6\">Explore AI Apps\n", "\t\t\t\t\t</a>\n", "\t\t\t\t\t<div aria-hidden=\"true\" class=\"pointer-events-none absolute bottom-0 left-1/2 h-full w-12 -translate-x-1/2 bg-white/45 blur-2xl transition-all group-hover:bg-white/55 dark:bg-white/25 dark:group-hover:bg-white/35\"></div>\n", "\t\t\t\t\t<div aria-hidden=\"true\" class=\"bg-linear-to-r pointer-events-none absolute inset-x-0 bottom-0 z-20 mx-auto h-px w-20 from-transparent via-white to-transparent opacity-45 transition-opacity group-hover:w-24 group-hover:opacity-75\"></div></div>\n", "\t\t\t\t<span class=\"lg:text-smd text-sm opacity-50 2xl:text-base\">or</span>\n", "\t\t\t\t<a href=\"/models\" class=\"text-gray-200! underline decoration-gray-600 underline-offset-8 transition-[text-underline-offset,text-decoration-color] hover:decoration-gray-300 hover:underline-offset-[6px]\">Browse 1M+ models</a></div>\n", "\t\t\t<div aria-hidden=\"true\" class=\"grainy pointer-events-none absolute right-2/3 h-[450px] w-[450px] translate-x-1/2 translate-y-32 rounded-full md:h-[600px] md:w-[600px] md:translate-y-2 lg:right-1/2 lg:translate-y-6\"></div>\n", "\t\t\t<img src=\"/front/assets/homepage/models-mobile.svg\" alt=\"Hugging Face models\" class=\"pointer-events-none relative mb-0 select-none lg:hidden\">\n", "\t\t\t<img src=\"/front/assets/homepage/models-tablet.svg\" alt=\"Hugging Face models\" class=\"pointer-events-none absolute bottom-0 right-0 hidden h-[650px] select-none lg:block xl:hidden 2xl:h-[760px]\">\n", "\t\t\t<img src=\"/front/assets/homepage/models.svg\" alt=\"Hugging Face models\" class=\"pointer-events-none absolute bottom-0 right-0 h-[650px] select-none max-xl:hidden 2xl:h-[760px]\">\n", "\t\t\t<div class=\"absolute inset-x-0 -bottom-4 z-10 h-5 w-full rounded-[50%] bg-white lg:h-6\"></div></div></div>\n", "\n", "\t<div class=\"container mx-auto mb-16 pt-12 sm:mb-32 sm:pt-20\"><div class=\"mb-10 flex items-center justify-center gap-2 text-xl font-bold sm:mb-14\"><div class=\"bg-linear-to-l mr-2 h-px flex-1 translate-y-px from-gray-200 to-white dark:from-gray-800 dark:to-gray-950\"></div>\n", "\t\t\tTrending on<img src=\"/front/assets/huggingface_logo-noborder.svg\" class=\"w-8\" alt=\"\">this week\n", "\t\t\t<div class=\"bg-linear-to-r ml-2 h-px flex-1 translate-y-px from-gray-200 to-white dark:from-gray-800 dark:to-gray-950\"></div></div>\n", "\t\t<div class=\"relative grid grid-cols-1 gap-6 lg:grid-cols-3\"><div class=\"bg-linear-to-br absolute h-full w-2/3 from-indigo-200/30 to-red-100/40 blur-2xl dark:from-indigo-500/10 dark:to-red-500/10 max-lg:translate-x-1/3 lg:h-96 lg:w-full lg:translate-y-24 lg:-rotate-2 lg:rounded-[50%] lg:from-indigo-200/80 lg:via-red-100/60 lg:to-gray-50 lg:dark:from-indigo-500/20 lg:dark:via-red-500/20 lg:dark:to-gray-950\"></div>\n", "\t\t\t<div class=\"relative col-span-1 flex flex-col items-stretch text-center\"><h2 class=\"mb-5 flex items-center justify-center gap-2 text-lg font-semibold 2xl:mb-6 2xl:text-xl\"><svg class=\"text-gray-500\" style=\"\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" aria-hidden=\"true\" focusable=\"false\" role=\"img\" width=\"1em\" height=\"1em\" preserveAspectRatio=\"xMidYMid meet\" viewBox=\"0 0 24 24\"><path class=\"uim-quaternary\" d=\"M20.23 7.24L12 12L3.77 7.24a1.98 1.98 0 0 1 .7-.71L11 2.76c.62-.35 1.38-.35 2 0l6.53 3.77c.29.173.531.418.7.71z\" opacity=\".25\" fill=\"currentColor\"></path><path class=\"uim-tertiary\" d=\"M12 12v9.5a2.09 2.09 0 0 1-.91-.21L4.5 17.48a2.003 2.003 0 0 1-1-1.73v-7.5a2.06 2.06 0 0 1 .27-1.01L12 12z\" opacity=\".5\" fill=\"currentColor\"></path><path class=\"uim-primary\" d=\"M20.5 8.25v7.5a2.003 2.003 0 0 1-1 1.73l-6.62 3.82c-.275.13-.576.198-.88.2V12l8.23-4.76c.175.308.268.656.27 1.01z\" fill=\"currentColor\"></path></svg>\n", "\t\t\t\t\tModels\n", "\t\t\t\t</h2>\n", "\t\t\t\t<div class=\"mb-3 flex flex-col gap-2.5 rounded-xl bg-white/40 p-3 backdrop-blur-lg dark:bg-gray-900/40 sm:mb-7\"><article class=\"overview-card-wrapper group/repo white \"><a class=\"flex items-center justify-between gap-4 p-2\" href=\"/microsoft/VibeVoice-1.5B\"><div class=\"w-full truncate\"><header class=\"flex items-center mb-0.5\" title=\"microsoft/VibeVoice-1.5B\">\n", "\t\t\t\t<h4 class=\"text-md truncate font-mono text-black dark:group-hover/repo:text-yellow-500 group-hover/repo:text-indigo-600 text-smd\">microsoft/VibeVoice-1.5B</h4>\n", "\t\t\t\t\n", "\t\t\t\t</header>\n", "\t\t\t<div class=\"mr-1 flex items-center overflow-hidden whitespace-nowrap text-sm leading-tight text-gray-400\">\n", "\t\t\t\t<span class=\"truncate\">Updated\n", "\t\t\t\t\t<time datetime=\"2025-08-28T04:57:59\" title=\"Thu, 28 Aug 2025 04:57:59 GMT\">2 days ago</time></span>\n", "\t\t\t\t\n", "\t\t\t\t<span class=\"px-1.5 text-gray-300 dark:text-gray-500\">• </span>\n", "\t\t\t\t\t<svg class=\"flex-none w-3 text-gray-400 mr-0.5\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" aria-hidden=\"true\" focusable=\"false\" role=\"img\" width=\"1em\" height=\"1em\" viewBox=\"0 0 32 32\"><path fill=\"currentColor\" d=\"M26 24v4H6v-4H4v4a2 2 0 0 0 2 2h20a2 2 0 0 0 2-2v-4zm0-10l-1.41-1.41L17 20.17V2h-2v18.17l-7.59-7.58L6 14l10 10l10-10z\"></path></svg>\n", "\t\t\t\t\t67.6k\n", "\t\t\t\t\n", "\t\t\t\t<span class=\"px-1.5 text-gray-300 dark:text-gray-500\">• </span>\n", "\t\t\t\t\t<svg class=\"flex-none w-3 text-gray-400 mr-1\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" aria-hidden=\"true\" focusable=\"false\" role=\"img\" width=\"1em\" height=\"1em\" preserveAspectRatio=\"xMidYMid meet\" viewBox=\"0 0 32 32\" fill=\"currentColor\"><path d=\"M22.45,6a5.47,5.47,0,0,1,3.91,1.64,5.7,5.7,0,0,1,0,8L16,26.13,5.64,15.64a5.7,5.7,0,0,1,0-8,5.48,5.48,0,0,1,7.82,0L16,10.24l2.53-2.58A5.44,5.44,0,0,1,22.45,6m0-2a7.47,7.47,0,0,0-5.34,2.24L16,7.36,14.89,6.24a7.49,7.49,0,0,0-10.68,0,7.72,7.72,0,0,0,0,10.82L16,29,27.79,17.06a7.72,7.72,0,0,0,0-10.82A7.49,7.49,0,0,0,22.45,4Z\"></path></svg>\n", "\t\t\t\t\t1.06k\n", "\n", "\t\t\t\t</div></div>\n", "\t\t</a></article><article class=\"overview-card-wrapper group/repo white \"><a class=\"flex items-center justify-between gap-4 p-2\" href=\"/xai-org/grok-2\"><div class=\"w-full truncate\"><header class=\"flex items-center mb-0.5\" title=\"xai-org/grok-2\">\n", "\t\t\t\t<h4 class=\"text-md truncate font-mono text-black dark:group-hover/repo:text-yellow-500 group-hover/repo:text-indigo-600 text-smd\">xai-org/grok-2</h4>\n", "\t\t\t\t\n", "\t\t\t\t</header>\n", "\t\t\t<div class=\"mr-1 flex items-center overflow-hidden whitespace-nowrap text-sm leading-tight text-gray-400\">\n", "\t\t\t\t<span class=\"truncate\">Updated\n", "\t\t\t\t\t<time datetime=\"2025-08-24T00:59:56\" title=\"Sun, 24 Aug 2025 00:59:56 GMT\">7 days ago</time></span>\n", "\t\t\t\t\n", "\t\t\t\t<span class=\"px-1.5 text-gray-300 dark:text-gray-500\">• </span>\n", "\t\t\t\t\t<svg class=\"flex-none w-3 text-gray-400 mr-0.5\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" aria-hidden=\"true\" focusable=\"false\" role=\"img\" width=\"1em\" height=\"1em\" viewBox=\"0 0 32 32\"><path fill=\"currentColor\" d=\"M26 24v4H6v-4H4v4a2 2 0 0 0 2 2h20a2 2 0 0 0 2-2v-4zm0-10l-1.41-1.41L17 20.17V2h-2v18.17l-7.59-7.58L6 14l10 10l10-10z\"></path></svg>\n", "\t\t\t\t\t3.99k\n", "\t\t\t\t\n", "\t\t\t\t<span class=\"px-1.5 text-gray-300 dark:text-gray-500\">• </span>\n", "\t\t\t\t\t<svg class=\"flex-none w-3 text-gray-400 mr-1\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" aria-hidden=\"true\" focusable=\"false\" role=\"img\" width=\"1em\" height=\"1em\" preserveAspectRatio=\"xMidYMid meet\" viewBox=\"0 0 32 32\" fill=\"currentColor\"><path d=\"M22.45,6a5.47,5.47,0,0,1,3.91,1.64,5.7,5.7,0,0,1,0,8L16,26.13,5.64,15.64a5.7,5.7,0,0,1,0-8,5.48,5.48,0,0,1,7.82,0L16,10.24l2.53-2.58A5.44,5.44,0,0,1,22.45,6m0-2a7.47,7.47,0,0,0-5.34,2.24L16,7.36,14.89,6.24a7.49,7.49,0,0,0-10.68,0,7.72,7.72,0,0,0,0,10.82L16,29,27.79,17.06a7.72,7.72,0,0,0,0-10.82A7.49,7.49,0,0,0,22.45,4Z\"></path></svg>\n", "\t\t\t\t\t862\n", "\n", "\t\t\t\t</div></div>\n", "\t\t</a></article><article class=\"overview-card-wrapper group/repo white \"><a class=\"flex items-center justify-between gap-4 p-2\" href=\"/openbmb/MiniCPM-V-4_5\"><div class=\"w-full truncate\"><header class=\"flex items-center mb-0.5\" title=\"openbmb/MiniCPM-V-4_5\">\n", "\t\t\t\t<h4 class=\"text-md truncate font-mono text-black dark:group-hover/repo:text-yellow-500 group-hover/repo:text-indigo-600 text-smd\">openbmb/MiniCPM-V-4_5</h4>\n", "\t\t\t\t\n", "\t\t\t\t</header>\n", "\t\t\t<div class=\"mr-1 flex items-center overflow-hidden whitespace-nowrap text-sm leading-tight text-gray-400\">\n", "\t\t\t\t<span class=\"truncate\">Updated\n", "\t\t\t\t\t<time datetime=\"2025-08-30T05:39:12\" title=\"Sat, 30 Aug 2025 05:39:12 GMT\">about 8 hours ago</time></span>\n", "\t\t\t\t\n", "\t\t\t\t<span class=\"px-1.5 text-gray-300 dark:text-gray-500\">• </span>\n", "\t\t\t\t\t<svg class=\"flex-none w-3 text-gray-400 mr-0.5\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" aria-hidden=\"true\" focusable=\"false\" role=\"img\" width=\"1em\" height=\"1em\" viewBox=\"0 0 32 32\"><path fill=\"currentColor\" d=\"M26 24v4H6v-4H4v4a2 2 0 0 0 2 2h20a2 2 0 0 0 2-2v-4zm0-10l-1.41-1.41L17 20.17V2h-2v18.17l-7.59-7.58L6 14l10 10l10-10z\"></path></svg>\n", "\t\t\t\t\t8.46k\n", "\t\t\t\t\n", "\t\t\t\t<span class=\"px-1.5 text-gray-300 dark:text-gray-500\">• </span>\n", "\t\t\t\t\t<svg class=\"flex-none w-3 text-gray-400 mr-1\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" aria-hidden=\"true\" focusable=\"false\" role=\"img\" width=\"1em\" height=\"1em\" preserveAspectRatio=\"xMidYMid meet\" viewBox=\"0 0 32 32\" fill=\"currentColor\"><path d=\"M22.45,6a5.47,5.47,0,0,1,3.91,1.64,5.7,5.7,0,0,1,0,8L16,26.13,5.64,15.64a5.7,5.7,0,0,1,0-8,5.48,5.48,0,0,1,7.82,0L16,10.24l2.53-2.58A5.44,5.44,0,0,1,22.45,6m0-2a7.47,7.47,0,0,0-5.34,2.24L16,7.36,14.89,6.24a7.49,7.49,0,0,0-10.68,0,7.72,7.72,0,0,0,0,10.82L16,29,27.79,17.06a7.72,7.72,0,0,0,0-10.82A7.49,7.49,0,0,0,22.45,4Z\"></path></svg>\n", "\t\t\t\t\t722\n", "\n", "\t\t\t\t</div></div>\n", "\t\t</a></article><article class=\"overview-card-wrapper group/repo white \"><a class=\"flex items-center justify-between gap-4 p-2\" href=\"/Qwen/Qwen-Image-Edit\"><div class=\"w-full truncate\"><header class=\"flex items-center mb-0.5\" title=\"Qwen/Qwen-Image-Edit\">\n", "\t\t\t\t<h4 class=\"text-md truncate font-mono text-black dark:group-hover/repo:text-yellow-500 group-hover/repo:text-indigo-600 text-smd\">Qwen/Qwen-Image-Edit</h4>\n", "\t\t\t\t\n", "\t\t\t\t</header>\n", "\t\t\t<div class=\"mr-1 flex items-center overflow-hidden whitespace-nowrap text-sm leading-tight text-gray-400\">\n", "\t\t\t\t<span class=\"truncate\">Updated\n", "\t\t\t\t\t<time datetime=\"2025-08-25T04:41:11\" title=\"Mon, 25 Aug 2025 04:41:11 GMT\">5 days ago</time></span>\n", "\t\t\t\t\n", "\t\t\t\t<span class=\"px-1.5 text-gray-300 dark:text-gray-500\">• </span>\n", "\t\t\t\t\t<svg class=\"flex-none w-3 text-gray-400 mr-0.5\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" aria-hidden=\"true\" focusable=\"false\" role=\"img\" width=\"1em\" height=\"1em\" viewBox=\"0 0 32 32\"><path fill=\"currentColor\" d=\"M26 24v4H6v-4H4v4a2 2 0 0 0 2 2h20a2 2 0 0 0 2-2v-4zm0-10l-1.41-1.41L17 20.17V2h-2v18.17l-7.59-7.58L6 14l10 10l10-10z\"></path></svg>\n", "\t\t\t\t\t72.3k\n", "\t\t\t\t\n", "\t\t\t\t<span class=\"px-1.5 text-gray-300 dark:text-gray-500\">• </span>\n", "\t\t\t\t\t<svg class=\"flex-none w-3 text-gray-400 mr-1\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" aria-hidden=\"true\" focusable=\"false\" role=\"img\" width=\"1em\" height=\"1em\" preserveAspectRatio=\"xMidYMid meet\" viewBox=\"0 0 32 32\" fill=\"currentColor\"><path d=\"M22.45,6a5.47,5.47,0,0,1,3.91,1.64,5.7,5.7,0,0,1,0,8L16,26.13,5.64,15.64a5.7,5.7,0,0,1,0-8,5.48,5.48,0,0,1,7.82,0L16,10.24l2.53-2.58A5.44,5.44,0,0,1,22.45,6m0-2a7.47,7.47,0,0,0-5.34,2.24L16,7.36,14.89,6.24a7.49,7.49,0,0,0-10.68,0,7.72,7.72,0,0,0,0,10.82L16,29,27.79,17.06a7.72,7.72,0,0,0,0-10.82A7.49,7.49,0,0,0,22.45,4Z\"></path></svg>\n", "\t\t\t\t\t1.53k\n", "\n", "\t\t\t\t</div></div>\n", "\t\t</a></article><article class=\"overview-card-wrapper group/repo white \"><a class=\"flex items-center justify-between gap-4 p-2\" href=\"/deepseek-ai/DeepSeek-V3.1\"><div class=\"w-full truncate\"><header class=\"flex items-center mb-0.5\" title=\"deepseek-ai/DeepSeek-V3.1\">\n", "\t\t\t\t<h4 class=\"text-md truncate font-mono text-black dark:group-hover/repo:text-yellow-500 group-hover/repo:text-indigo-600 text-smd\">deepseek-ai/DeepSeek-V3.1</h4>\n", "\t\t\t\t\n", "\t\t\t\t</header>\n", "\t\t\t<div class=\"mr-1 flex items-center overflow-hidden whitespace-nowrap text-sm leading-tight text-gray-400\">\n", "\t\t\t\t<span class=\"truncate\">Updated\n", "\t\t\t\t\t<time datetime=\"2025-08-26T08:14:11\" title=\"<PERSON><PERSON>, 26 Aug 2025 08:14:11 GMT\">4 days ago</time></span>\n", "\t\t\t\t\n", "\t\t\t\t<span class=\"px-1.5 text-gray-300 dark:text-gray-500\">• </span>\n", "\t\t\t\t\t<svg class=\"flex-none w-3 text-gray-400 mr-0.5\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" aria-hidden=\"true\" focusable=\"false\" role=\"img\" width=\"1em\" height=\"1em\" viewBox=\"0 0 32 32\"><path fill=\"currentColor\" d=\"M26 24v4H6v-4H4v4a2 2 0 0 0 2 2h20a2 2 0 0 0 2-2v-4zm0-10l-1.41-1.41L17 20.17V2h-2v18.17l-7.59-7.58L6 14l10 10l10-10z\"></path></svg>\n", "\t\t\t\t\t72.4k\n", "\t\t\t\t\n", "\t\t\t\t<span class=\"px-1.5 text-gray-300 dark:text-gray-500\">• </span>\n", "\t\t\t\t\t<svg class=\"flex-none w-3 text-gray-400 mr-1\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" aria-hidden=\"true\" focusable=\"false\" role=\"img\" width=\"1em\" height=\"1em\" preserveAspectRatio=\"xMidYMid meet\" viewBox=\"0 0 32 32\" fill=\"currentColor\"><path d=\"M22.45,6a5.47,5.47,0,0,1,3.91,1.64,5.7,5.7,0,0,1,0,8L16,26.13,5.64,15.64a5.7,5.7,0,0,1,0-8,5.48,5.48,0,0,1,7.82,0L16,10.24l2.53-2.58A5.44,5.44,0,0,1,22.45,6m0-2a7.47,7.47,0,0,0-5.34,2.24L16,7.36,14.89,6.24a7.49,7.49,0,0,0-10.68,0,7.72,7.72,0,0,0,0,10.82L16,29,27.79,17.06a7.72,7.72,0,0,0,0-10.82A7.49,7.49,0,0,0,22.45,4Z\"></path></svg>\n", "\t\t\t\t\t660\n", "\n", "\t\t\t\t</div></div>\n", "\t\t</a></article></div>\n", "\t\t\t\t<a href=\"/models\" class=\"self-center underline decoration-gray-300 underline-offset-8 transition-all hover:decoration-gray-800 hover:underline-offset-[6px] dark:text-gray-300 dark:decoration-gray-600 dark:hover:decoration-gray-300 max-sm:mb-5\">Browse 1M+ models</a></div>\n", "\t\t\t<div class=\"relative col-span-1 flex flex-col items-stretch text-center\"><h2 class=\"mb-5 flex items-center justify-center gap-2 text-lg font-semibold 2xl:mb-6 2xl:text-xl\"><svg class=\"\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" aria-hidden=\"true\" focusable=\"false\" role=\"img\" width=\"1em\" height=\"1em\" preserveAspectRatio=\"xMidYMid meet\" viewBox=\"0 0 32 32\"><path d=\"M7.80914 18.7462V24.1907H13.2536V18.7462H7.80914Z\" fill=\"#FF3270\"></path><path d=\"M18.7458 18.7462V24.1907H24.1903V18.7462H18.7458Z\" fill=\"#861FFF\"></path><path d=\"M7.80914 7.80982V13.2543H13.2536V7.80982H7.80914Z\" fill=\"#097EFF\"></path><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M4 6.41775C4 5.08246 5.08246 4 6.41775 4H14.6457C15.7626 4 16.7026 4.75724 16.9802 5.78629C18.1505 4.67902 19.7302 4 21.4685 4C25.0758 4 28.0003 6.92436 28.0003 10.5317C28.0003 12.27 27.3212 13.8497 26.2139 15.02C27.243 15.2977 28.0003 16.2376 28.0003 17.3545V25.5824C28.0003 26.9177 26.9177 28.0003 25.5824 28.0003H17.0635H14.9367H6.41775C5.08246 28.0003 4 26.9177 4 25.5824V15.1587V14.9367V6.41775ZM7.80952 7.80952V13.254H13.254V7.80952H7.80952ZM7.80952 24.1907V18.7462H13.254V24.1907H7.80952ZM18.7462 24.1907V18.7462H24.1907V24.1907H18.7462ZM18.7462 10.5317C18.7462 9.0283 19.9651 7.80952 21.4685 7.80952C22.9719 7.80952 24.1907 9.0283 24.1907 10.5317C24.1907 12.0352 22.9719 13.254 21.4685 13.254C19.9651 13.254 18.7462 12.0352 18.7462 10.5317Z\" fill=\"black\"></path><path d=\"M21.4681 7.80982C19.9647 7.80982 18.7458 9.02861 18.7458 10.5321C18.7458 12.0355 19.9647 13.2543 21.4681 13.2543C22.9715 13.2543 24.1903 12.0355 24.1903 10.5321C24.1903 9.02861 22.9715 7.80982 21.4681 7.80982Z\" fill=\"#FFD702\"></path></svg>\n", "\t\t\t\t\tSpaces\n", "\t\t\t\t</h2>\n", "\t\t\t\t<div class=\"mb-3 flex flex-col gap-2.5 rounded-xl bg-white/40 p-3 backdrop-blur-lg dark:bg-gray-900/40 sm:mb-7\"><article class=\" relative\">\n", "\t<a href=\"/spaces/enzostvs/deepsite\" class=\"bg-linear-to-br group relative z-0 mx-auto flex flex-col justify-between overflow-hidden hover:shadow-inner from-blue-600 to-blue-600 shadow-sm dark:bg-gray-900 hover:brightness-110 h-15.5 rounded-lg\"><div class=\"bg-linear-to-br absolute left-0 top-0 h-full w-1/2 from-black/20 via-transparent to-transparent\"></div>\n", "\t\t<header class=\"@container bg-linear-to-t flex gap-x-1 overflow-hidden rounded-b-2xl bg-black/[0.04] from-black/[0.04] to-transparent to-10% lg:gap-x-1.5 hidden\"><div class=\"mt-[8.5px] flex h-[16px] flex-wrap gap-x-1 gap-y-5 lg:gap-x-1.5\">\n", "\t\t\t\t<div class=\"cursor-pointer select-none overflow-hidden font-mono  border-white/5! inline-flex items-center rounded-sm border bg-white/10 leading-tight text-white opacity-90\"><div class=\"inline-flex items-center px-1 py-0\">\n", "\t\tRunning\n", "\t\t</div>\n", "\t</div>\n", "\t\t\t\t\n", "\t\t\t\t\n", "\t\t\t\t\n", "\t\t\t\t</div>\n", "\n", "\t\t\t<div class=\"flex items-center justify-end undefined ml-auto mt-[8.5px] h-[16px] rounded border !border-white/5 bg-white/10 px-1 dark:!border-white/5\">\n", "\t\t\t\t\t<svg class=\"mr-1 text-gray-100\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" aria-hidden=\"true\" focusable=\"false\" role=\"img\" width=\"1em\" height=\"1em\" preserveAspectRatio=\"xMidYMid meet\" viewBox=\"0 0 32 32\" fill=\"currentColor\"><path d=\"M22.45,6a5.47,5.47,0,0,1,3.91,1.64,5.7,5.7,0,0,1,0,8L16,26.13,5.64,15.64a5.7,5.7,0,0,1,0-8,5.48,5.48,0,0,1,7.82,0L16,10.24l2.53-2.58A5.44,5.44,0,0,1,22.45,6m0-2a7.47,7.47,0,0,0-5.34,2.24L16,7.36,14.89,6.24a7.49,7.49,0,0,0-10.68,0,7.72,7.72,0,0,0,0,10.82L16,29,27.79,17.06a7.72,7.72,0,0,0,0-10.82A7.49,7.49,0,0,0,22.45,4Z\"></path></svg>\n", "\t\t\t\t\t<span class=\"text-white\">12.5k</span></div></header>\n", "\n", "\t\t<div class=\"absolute right-3 top-[0.6rem] flex items-center text-xs\">\n", "\t\t\t\t<svg class=\"mr-1 text-gray-100\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" aria-hidden=\"true\" focusable=\"false\" role=\"img\" width=\"1em\" height=\"1em\" preserveAspectRatio=\"xMidYMid meet\" viewBox=\"0 0 32 32\" fill=\"currentColor\"><path d=\"M22.45,6a5.47,5.47,0,0,1,3.91,1.64,5.7,5.7,0,0,1,0,8L16,26.13,5.64,15.64a5.7,5.7,0,0,1,0-8,5.48,5.48,0,0,1,7.82,0L16,10.24l2.53-2.58A5.44,5.44,0,0,1,22.45,6m0-2a7.47,7.47,0,0,0-5.34,2.24L16,7.36,14.89,6.24a7.49,7.49,0,0,0-10.68,0,7.72,7.72,0,0,0,0,10.82L16,29,27.79,17.06a7.72,7.72,0,0,0,0-10.82A7.49,7.49,0,0,0,22.45,4Z\"></path></svg>\n", "\t\t\t\t<span class=\"text-white\">12.5k</span></div>\n", "\n", "\t\t<main class=\"px-3 flex flex-col justify-center h-full pt-0.5 relative\"><div class=\"mb-0.5 flex items-center justify-start gap-1.5 font-semibold leading-tight text-white text-sm\"><h4 class=\"line-clamp-2 text-balance text-left drop-shadow-lg\">DeepSite v2</h4>\n", "\t\t\t\t<div class=\"leading-none drop-shadow-2xl\">🐳</div></div>\n", "\t\t\t<p class=\"line-clamp-1 text-left text-[0.8375rem]/[1.15rem] text-gray-200 opacity-85 drop-shadow-sm\">Generate any application with DeepSeek</p></main>\n", "\t\t</a></article><article class=\" relative\">\n", "\t<a href=\"/spaces/zerogpu-aoti/wan2-2-fp8da-aoti-faster\" class=\"bg-linear-to-br group relative z-0 mx-auto flex flex-col justify-between overflow-hidden hover:shadow-inner from-gray-600 to-pink-600 shadow-sm dark:bg-gray-900 hover:brightness-110 h-15.5 rounded-lg\"><div class=\"bg-linear-to-br absolute left-0 top-0 h-full w-1/2 from-black/20 via-transparent to-transparent\"></div>\n", "\t\t<header class=\"@container bg-linear-to-t flex gap-x-1 overflow-hidden rounded-b-2xl bg-black/[0.04] from-black/[0.04] to-transparent to-10% lg:gap-x-1.5 hidden\"><div class=\"mt-[8.5px] flex h-[16px] flex-wrap gap-x-1 gap-y-5 lg:gap-x-1.5\">\n", "\t\t\t\t<div class=\"cursor-pointer select-none overflow-hidden font-mono  border-white/5! inline-flex items-center rounded-sm border bg-white/10 leading-tight text-white opacity-90\"><div class=\"inline-flex items-center px-1 py-0\">\n", "\t\tRunning\n", "\t\t\n", "\t\t\t<span class=\"mx-1\">on </span>\n", "\t\t\t<svg class=\"size-[.6rem] mr-[0.1875rem] animate-spin-slow-reversed [animation-duration:6s]\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" aria-hidden=\"true\" focusable=\"false\" role=\"img\" width=\"1em\" height=\"1em\" preserveAspectRatio=\"xMidYMid meet\" viewBox=\"0 0 15 15\"><path d=\"M7.48877 6.75C7.29015 6.75 7.09967 6.82902 6.95923 6.96967C6.81879 7.11032 6.73989 7.30109 6.73989 7.5C6.73989 7.69891 6.81879 7.88968 6.95923 8.03033C7.09967 8.17098 7.29015 8.25 7.48877 8.25C7.68738 8.25 7.87786 8.17098 8.0183 8.03033C8.15874 7.88968 8.23764 7.69891 8.23764 7.5C8.23764 7.30109 8.15874 7.11032 8.0183 6.96967C7.87786 6.82902 7.68738 6.75 7.48877 6.75ZM7.8632 0C11.2331 0 11.3155 2.6775 9.54818 3.5625C8.80679 3.93 8.47728 4.7175 8.335 5.415C8.69446 5.565 9.00899 5.7975 9.24863 6.0975C12.0195 4.5975 15 5.19 15 7.875C15 11.25 12.3265 11.325 11.4428 9.5475C11.0684 8.805 10.2746 8.475 9.57813 8.3325C9.42836 8.6925 9.19621 9 8.89665 9.255C10.3869 12.0225 9.79531 15 7.11433 15C3.74438 15 3.67698 12.315 5.44433 11.43C6.17823 11.0625 6.50774 10.2825 6.65751 9.5925C6.29056 9.4425 5.96855 9.2025 5.72891 8.9025C2.96555 10.3875 0 9.8025 0 7.125C0 3.75 2.666 3.6675 3.54967 5.445C3.92411 6.1875 4.71043 6.51 5.40689 6.6525C5.54918 6.2925 5.78882 5.9775 6.09586 5.7375C4.60559 2.97 5.1972 0 7.8632 0Z\" fill=\"currentColor\"></path></svg>\n", "\t\t\t<span class=\"-skew-x-6 truncate font-bold uppercase\">Zero</span></div>\n", "\t</div>\n", "\t\t\t\t<div class=\"border-white/5! inline-flex h-[16.5px] items-center justify-center gap-0.5 rounded-sm border bg-white/10 pl-0.5 pr-1 leading-tight text-white opacity-90\"><svg class=\"size-3\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" aria-hidden=\"true\" fill=\"currentColor\" focusable=\"false\" role=\"img\" width=\"1em\" height=\"1em\" preserveAspectRatio=\"xMidYMid meet\" viewBox=\"0 0 32 32\"><path d=\"M14.82 3.56a4.78 4.78 0 0 1 6.67.09l.09.09a4.77 4.77 0 0 1 1.27 3.96c1.4-.2 2.87.23 3.97 1.27l.08.09.06.05.09.1a4.78 4.78 0 0 1 0 6.58l-.09.09-9.78 9.78-.05.05c-.08.12-.06.3.05.4l2 2.01a.96.96 0 0 1-1.35 1.35l-2-2a2.23 2.23 0 0 1-.05-3.12l.04-.04 9.79-9.79.05-.05a2.87 2.87 0 0 0-.05-4l-.06-.06-.05-.05a2.87 2.87 0 0 0-3.95 0l-.02.02-.04.03-8.17 8.17-.03.04a.96.96 0 0 1-1.32-1.39l.1-.1.02-.02 8.1-8.1a2.87 2.87 0 0 0-.05-4l-.21-.2a2.87 2.87 0 0 0-3.8.14l-.05.05L5.26 15.82l-.03.04a.96.96 0 0 1-1.32-1.39L14.73 3.65l.09-.09Zm2.65 2.76a.96.96 0 0 1 1.32 1.39l-8 8a2.87 2.87 0 0 0 4.05 4.06l8-8a.96.96 0 0 1 1.36 1.35l-8 8a4.78 4.78 0 0 1-6.68.09l-.09-.09a4.78 4.78 0 0 1 0-6.76l8-8 .04-.04Z\" fill=\"currentColor\"></path></svg>\n", "\t\t\t\t\t\t<span class=\"@max-2xs:hidden\">MCP</span></div>\n", "\t\t\t\t\n", "\t\t\t\t\n", "\t\t\t\t</div>\n", "\n", "\t\t\t<div class=\"flex items-center justify-end undefined ml-auto mt-[8.5px] h-[16px] rounded border !border-white/5 bg-white/10 px-1 dark:!border-white/5\">\n", "\t\t\t\t\t<svg class=\"mr-1 text-gray-100\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" aria-hidden=\"true\" focusable=\"false\" role=\"img\" width=\"1em\" height=\"1em\" preserveAspectRatio=\"xMidYMid meet\" viewBox=\"0 0 32 32\" fill=\"currentColor\"><path d=\"M22.45,6a5.47,5.47,0,0,1,3.91,1.64,5.7,5.7,0,0,1,0,8L16,26.13,5.64,15.64a5.7,5.7,0,0,1,0-8,5.48,5.48,0,0,1,7.82,0L16,10.24l2.53-2.58A5.44,5.44,0,0,1,22.45,6m0-2a7.47,7.47,0,0,0-5.34,2.24L16,7.36,14.89,6.24a7.49,7.49,0,0,0-10.68,0,7.72,7.72,0,0,0,0,10.82L16,29,27.79,17.06a7.72,7.72,0,0,0,0-10.82A7.49,7.49,0,0,0,22.45,4Z\"></path></svg>\n", "\t\t\t\t\t<span class=\"text-white\">542</span></div></header>\n", "\n", "\t\t<div class=\"absolute right-3 top-[0.6rem] flex items-center text-xs\">\n", "\t\t\t\t<svg class=\"mr-1 text-gray-100\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" aria-hidden=\"true\" focusable=\"false\" role=\"img\" width=\"1em\" height=\"1em\" preserveAspectRatio=\"xMidYMid meet\" viewBox=\"0 0 32 32\" fill=\"currentColor\"><path d=\"M22.45,6a5.47,5.47,0,0,1,3.91,1.64,5.7,5.7,0,0,1,0,8L16,26.13,5.64,15.64a5.7,5.7,0,0,1,0-8,5.48,5.48,0,0,1,7.82,0L16,10.24l2.53-2.58A5.44,5.44,0,0,1,22.45,6m0-2a7.47,7.47,0,0,0-5.34,2.24L16,7.36,14.89,6.24a7.49,7.49,0,0,0-10.68,0,7.72,7.72,0,0,0,0,10.82L16,29,27.79,17.06a7.72,7.72,0,0,0,0-10.82A7.49,7.49,0,0,0,22.45,4Z\"></path></svg>\n", "\t\t\t\t<span class=\"text-white\">542</span></div>\n", "\n", "\t\t<main class=\"px-3 flex flex-col justify-center h-full pt-0.5 relative\"><div class=\"mb-0.5 flex items-center justify-start gap-1.5 font-semibold leading-tight text-white text-sm\"><h4 class=\"line-clamp-2 text-balance text-left drop-shadow-lg\">Wan2.2 14B Fast</h4>\n", "\t\t\t\t<div class=\"leading-none drop-shadow-2xl\">🎥</div></div>\n", "\t\t\t<p class=\"line-clamp-1 text-left text-[0.8375rem]/[1.15rem] text-gray-200 opacity-85 drop-shadow-sm\">generate a video from an image with a text prompt</p></main>\n", "\t\t</a></article><article class=\" relative\">\n", "\t<a href=\"/spaces/multimodalart/Qwen-Image-Edit-Fast\" class=\"bg-linear-to-br group relative z-0 mx-auto flex flex-col justify-between overflow-hidden hover:shadow-inner from-pink-600 to-green-600 shadow-sm dark:bg-gray-900 hover:brightness-110 h-15.5 rounded-lg\"><div class=\"bg-linear-to-br absolute left-0 top-0 h-full w-1/2 from-black/20 via-transparent to-transparent\"></div>\n", "\t\t<header class=\"@container bg-linear-to-t flex gap-x-1 overflow-hidden rounded-b-2xl bg-black/[0.04] from-black/[0.04] to-transparent to-10% lg:gap-x-1.5 hidden\"><div class=\"mt-[8.5px] flex h-[16px] flex-wrap gap-x-1 gap-y-5 lg:gap-x-1.5\">\n", "\t\t\t\t<div class=\"cursor-pointer select-none overflow-hidden font-mono  border-white/5! inline-flex items-center rounded-sm border bg-white/10 leading-tight text-white opacity-90\"><div class=\"inline-flex items-center px-1 py-0\">\n", "\t\tRunning\n", "\t\t\n", "\t\t\t<span class=\"mx-1\">on </span>\n", "\t\t\t<svg class=\"size-[.6rem] mr-[0.1875rem] animate-spin-slow-reversed [animation-duration:6s]\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" aria-hidden=\"true\" focusable=\"false\" role=\"img\" width=\"1em\" height=\"1em\" preserveAspectRatio=\"xMidYMid meet\" viewBox=\"0 0 15 15\"><path d=\"M7.48877 6.75C7.29015 6.75 7.09967 6.82902 6.95923 6.96967C6.81879 7.11032 6.73989 7.30109 6.73989 7.5C6.73989 7.69891 6.81879 7.88968 6.95923 8.03033C7.09967 8.17098 7.29015 8.25 7.48877 8.25C7.68738 8.25 7.87786 8.17098 8.0183 8.03033C8.15874 7.88968 8.23764 7.69891 8.23764 7.5C8.23764 7.30109 8.15874 7.11032 8.0183 6.96967C7.87786 6.82902 7.68738 6.75 7.48877 6.75ZM7.8632 0C11.2331 0 11.3155 2.6775 9.54818 3.5625C8.80679 3.93 8.47728 4.7175 8.335 5.415C8.69446 5.565 9.00899 5.7975 9.24863 6.0975C12.0195 4.5975 15 5.19 15 7.875C15 11.25 12.3265 11.325 11.4428 9.5475C11.0684 8.805 10.2746 8.475 9.57813 8.3325C9.42836 8.6925 9.19621 9 8.89665 9.255C10.3869 12.0225 9.79531 15 7.11433 15C3.74438 15 3.67698 12.315 5.44433 11.43C6.17823 11.0625 6.50774 10.2825 6.65751 9.5925C6.29056 9.4425 5.96855 9.2025 5.72891 8.9025C2.96555 10.3875 0 9.8025 0 7.125C0 3.75 2.666 3.6675 3.54967 5.445C3.92411 6.1875 4.71043 6.51 5.40689 6.6525C5.54918 6.2925 5.78882 5.9775 6.09586 5.7375C4.60559 2.97 5.1972 0 7.8632 0Z\" fill=\"currentColor\"></path></svg>\n", "\t\t\t<span class=\"-skew-x-6 truncate font-bold uppercase\">Zero</span></div>\n", "\t</div>\n", "\t\t\t\t\n", "\t\t\t\t\n", "\t\t\t\t\n", "\t\t\t\t</div>\n", "\n", "\t\t\t<div class=\"flex items-center justify-end undefined ml-auto mt-[8.5px] h-[16px] rounded border !border-white/5 bg-white/10 px-1 dark:!border-white/5\">\n", "\t\t\t\t\t<svg class=\"mr-1 text-gray-100\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" aria-hidden=\"true\" focusable=\"false\" role=\"img\" width=\"1em\" height=\"1em\" preserveAspectRatio=\"xMidYMid meet\" viewBox=\"0 0 32 32\" fill=\"currentColor\"><path d=\"M22.45,6a5.47,5.47,0,0,1,3.91,1.64,5.7,5.7,0,0,1,0,8L16,26.13,5.64,15.64a5.7,5.7,0,0,1,0-8,5.48,5.48,0,0,1,7.82,0L16,10.24l2.53-2.58A5.44,5.44,0,0,1,22.45,6m0-2a7.47,7.47,0,0,0-5.34,2.24L16,7.36,14.89,6.24a7.49,7.49,0,0,0-10.68,0,7.72,7.72,0,0,0,0,10.82L16,29,27.79,17.06a7.72,7.72,0,0,0,0-10.82A7.49,7.49,0,0,0,22.45,4Z\"></path></svg>\n", "\t\t\t\t\t<span class=\"text-white\">201</span></div></header>\n", "\n", "\t\t<div class=\"absolute right-3 top-[0.6rem] flex items-center text-xs\">\n", "\t\t\t\t<svg class=\"mr-1 text-gray-100\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" aria-hidden=\"true\" focusable=\"false\" role=\"img\" width=\"1em\" height=\"1em\" preserveAspectRatio=\"xMidYMid meet\" viewBox=\"0 0 32 32\" fill=\"currentColor\"><path d=\"M22.45,6a5.47,5.47,0,0,1,3.91,1.64,5.7,5.7,0,0,1,0,8L16,26.13,5.64,15.64a5.7,5.7,0,0,1,0-8,5.48,5.48,0,0,1,7.82,0L16,10.24l2.53-2.58A5.44,5.44,0,0,1,22.45,6m0-2a7.47,7.47,0,0,0-5.34,2.24L16,7.36,14.89,6.24a7.49,7.49,0,0,0-10.68,0,7.72,7.72,0,0,0,0,10.82L16,29,27.79,17.06a7.72,7.72,0,0,0,0-10.82A7.49,7.49,0,0,0,22.45,4Z\"></path></svg>\n", "\t\t\t\t<span class=\"text-white\">201</span></div>\n", "\n", "\t\t<main class=\"px-3 flex flex-col justify-center h-full pt-0.5 relative\"><div class=\"mb-0.5 flex items-center justify-start gap-1.5 font-semibold leading-tight text-white text-sm\"><h4 class=\"line-clamp-2 text-balance text-left drop-shadow-lg\">Qwen Image Edit Fast!</h4>\n", "\t\t\t\t<div class=\"leading-none drop-shadow-2xl\">✒</div></div>\n", "\t\t\t<p class=\"line-clamp-1 text-left text-[0.8375rem]/[1.15rem] text-gray-200 opacity-85 drop-shadow-sm\">Fast 8 step inference of Qwen Image Edit</p></main>\n", "\t\t</a></article><article class=\" relative\">\n", "\t<a href=\"/spaces/Qwen/Qwen-Image-Edit\" class=\"bg-linear-to-br group relative z-0 mx-auto flex flex-col justify-between overflow-hidden hover:shadow-inner from-pink-600 to-green-600 shadow-sm dark:bg-gray-900 hover:brightness-110 h-15.5 rounded-lg\"><div class=\"bg-linear-to-br absolute left-0 top-0 h-full w-1/2 from-black/20 via-transparent to-transparent\"></div>\n", "\t\t<header class=\"@container bg-linear-to-t flex gap-x-1 overflow-hidden rounded-b-2xl bg-black/[0.04] from-black/[0.04] to-transparent to-10% lg:gap-x-1.5 hidden\"><div class=\"mt-[8.5px] flex h-[16px] flex-wrap gap-x-1 gap-y-5 lg:gap-x-1.5\">\n", "\t\t\t\t<div class=\"cursor-pointer select-none overflow-hidden font-mono  border-white/5! inline-flex items-center rounded-sm border bg-white/10 leading-tight text-white opacity-90\"><div class=\"inline-flex items-center px-1 py-0\">\n", "\t\tRunning\n", "\t\t\n", "\t\t\t<span class=\"mx-1\">on </span>\n", "\t\t\t<svg class=\"size-[.6rem] mr-[0.1875rem] animate-spin-slow-reversed [animation-duration:6s]\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" aria-hidden=\"true\" focusable=\"false\" role=\"img\" width=\"1em\" height=\"1em\" preserveAspectRatio=\"xMidYMid meet\" viewBox=\"0 0 15 15\"><path d=\"M7.48877 6.75C7.29015 6.75 7.09967 6.82902 6.95923 6.96967C6.81879 7.11032 6.73989 7.30109 6.73989 7.5C6.73989 7.69891 6.81879 7.88968 6.95923 8.03033C7.09967 8.17098 7.29015 8.25 7.48877 8.25C7.68738 8.25 7.87786 8.17098 8.0183 8.03033C8.15874 7.88968 8.23764 7.69891 8.23764 7.5C8.23764 7.30109 8.15874 7.11032 8.0183 6.96967C7.87786 6.82902 7.68738 6.75 7.48877 6.75ZM7.8632 0C11.2331 0 11.3155 2.6775 9.54818 3.5625C8.80679 3.93 8.47728 4.7175 8.335 5.415C8.69446 5.565 9.00899 5.7975 9.24863 6.0975C12.0195 4.5975 15 5.19 15 7.875C15 11.25 12.3265 11.325 11.4428 9.5475C11.0684 8.805 10.2746 8.475 9.57813 8.3325C9.42836 8.6925 9.19621 9 8.89665 9.255C10.3869 12.0225 9.79531 15 7.11433 15C3.74438 15 3.67698 12.315 5.44433 11.43C6.17823 11.0625 6.50774 10.2825 6.65751 9.5925C6.29056 9.4425 5.96855 9.2025 5.72891 8.9025C2.96555 10.3875 0 9.8025 0 7.125C0 3.75 2.666 3.6675 3.54967 5.445C3.92411 6.1875 4.71043 6.51 5.40689 6.6525C5.54918 6.2925 5.78882 5.9775 6.09586 5.7375C4.60559 2.97 5.1972 0 7.8632 0Z\" fill=\"currentColor\"></path></svg>\n", "\t\t\t<span class=\"-skew-x-6 truncate font-bold uppercase\">Zero</span></div>\n", "\t</div>\n", "\t\t\t\t\n", "\t\t\t\t\n", "\t\t\t\t\n", "\t\t\t\t</div>\n", "\n", "\t\t\t<div class=\"flex items-center justify-end undefined ml-auto mt-[8.5px] h-[16px] rounded border !border-white/5 bg-white/10 px-1 dark:!border-white/5\">\n", "\t\t\t\t\t<svg class=\"mr-1 text-gray-100\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" aria-hidden=\"true\" focusable=\"false\" role=\"img\" width=\"1em\" height=\"1em\" preserveAspectRatio=\"xMidYMid meet\" viewBox=\"0 0 32 32\" fill=\"currentColor\"><path d=\"M22.45,6a5.47,5.47,0,0,1,3.91,1.64,5.7,5.7,0,0,1,0,8L16,26.13,5.64,15.64a5.7,5.7,0,0,1,0-8,5.48,5.48,0,0,1,7.82,0L16,10.24l2.53-2.58A5.44,5.44,0,0,1,22.45,6m0-2a7.47,7.47,0,0,0-5.34,2.24L16,7.36,14.89,6.24a7.49,7.49,0,0,0-10.68,0,7.72,7.72,0,0,0,0,10.82L16,29,27.79,17.06a7.72,7.72,0,0,0,0-10.82A7.49,7.49,0,0,0,22.45,4Z\"></path></svg>\n", "\t\t\t\t\t<span class=\"text-white\">431</span></div></header>\n", "\n", "\t\t<div class=\"absolute right-3 top-[0.6rem] flex items-center text-xs\">\n", "\t\t\t\t<svg class=\"mr-1 text-gray-100\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" aria-hidden=\"true\" focusable=\"false\" role=\"img\" width=\"1em\" height=\"1em\" preserveAspectRatio=\"xMidYMid meet\" viewBox=\"0 0 32 32\" fill=\"currentColor\"><path d=\"M22.45,6a5.47,5.47,0,0,1,3.91,1.64,5.7,5.7,0,0,1,0,8L16,26.13,5.64,15.64a5.7,5.7,0,0,1,0-8,5.48,5.48,0,0,1,7.82,0L16,10.24l2.53-2.58A5.44,5.44,0,0,1,22.45,6m0-2a7.47,7.47,0,0,0-5.34,2.24L16,7.36,14.89,6.24a7.49,7.49,0,0,0-10.68,0,7.72,7.72,0,0,0,0,10.82L16,29,27.79,17.06a7.72,7.72,0,0,0,0-10.82A7.49,7.49,0,0,0,22.45,4Z\"></path></svg>\n", "\t\t\t\t<span class=\"text-white\">431</span></div>\n", "\n", "\t\t<main class=\"px-3 flex flex-col justify-center h-full pt-0.5 relative\"><div class=\"mb-0.5 flex items-center justify-start gap-1.5 font-semibold leading-tight text-white text-sm\"><h4 class=\"line-clamp-2 text-balance text-left drop-shadow-lg\">Qwen Image Edit</h4>\n", "\t\t\t\t<div class=\"leading-none drop-shadow-2xl\">✒</div></div>\n", "\t\t\t<p class=\"line-clamp-1 text-left text-[0.8375rem]/[1.15rem] text-gray-200 opacity-85 drop-shadow-sm\">Edit images based on user instructions</p></main>\n", "\t\t</a></article><article class=\" relative\">\n", "\t<a href=\"/spaces/syncora/synthetic-generation\" class=\"bg-linear-to-br group relative z-0 mx-auto flex flex-col justify-between overflow-hidden hover:shadow-inner from-indigo-600 to-red-600 shadow-sm dark:bg-gray-900 hover:brightness-110 h-15.5 rounded-lg\"><div class=\"bg-linear-to-br absolute left-0 top-0 h-full w-1/2 from-black/20 via-transparent to-transparent\"></div>\n", "\t\t<header class=\"@container bg-linear-to-t flex gap-x-1 overflow-hidden rounded-b-2xl bg-black/[0.04] from-black/[0.04] to-transparent to-10% lg:gap-x-1.5 hidden\"><div class=\"mt-[8.5px] flex h-[16px] flex-wrap gap-x-1 gap-y-5 lg:gap-x-1.5\">\n", "\t\t\t\t<div class=\"cursor-pointer select-none overflow-hidden font-mono  border-white/5! inline-flex items-center rounded-sm border bg-white/10 leading-tight text-white opacity-90\"><div class=\"inline-flex items-center px-1 py-0\">\n", "\t\tRunning\n", "\t\t</div>\n", "\t</div>\n", "\t\t\t\t\n", "\t\t\t\t\n", "\t\t\t\t\n", "\t\t\t\t</div>\n", "\n", "\t\t\t<div class=\"flex items-center justify-end undefined ml-auto mt-[8.5px] h-[16px] rounded border !border-white/5 bg-white/10 px-1 dark:!border-white/5\">\n", "\t\t\t\t\t<svg class=\"mr-1 text-gray-100\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" aria-hidden=\"true\" focusable=\"false\" role=\"img\" width=\"1em\" height=\"1em\" preserveAspectRatio=\"xMidYMid meet\" viewBox=\"0 0 32 32\" fill=\"currentColor\"><path d=\"M22.45,6a5.47,5.47,0,0,1,3.91,1.64,5.7,5.7,0,0,1,0,8L16,26.13,5.64,15.64a5.7,5.7,0,0,1,0-8,5.48,5.48,0,0,1,7.82,0L16,10.24l2.53-2.58A5.44,5.44,0,0,1,22.45,6m0-2a7.47,7.47,0,0,0-5.34,2.24L16,7.36,14.89,6.24a7.49,7.49,0,0,0-10.68,0,7.72,7.72,0,0,0,0,10.82L16,29,27.79,17.06a7.72,7.72,0,0,0,0-10.82A7.49,7.49,0,0,0,22.45,4Z\"></path></svg>\n", "\t\t\t\t\t<span class=\"text-white\">148</span></div></header>\n", "\n", "\t\t<div class=\"absolute right-3 top-[0.6rem] flex items-center text-xs\">\n", "\t\t\t\t<svg class=\"mr-1 text-gray-100\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" aria-hidden=\"true\" focusable=\"false\" role=\"img\" width=\"1em\" height=\"1em\" preserveAspectRatio=\"xMidYMid meet\" viewBox=\"0 0 32 32\" fill=\"currentColor\"><path d=\"M22.45,6a5.47,5.47,0,0,1,3.91,1.64,5.7,5.7,0,0,1,0,8L16,26.13,5.64,15.64a5.7,5.7,0,0,1,0-8,5.48,5.48,0,0,1,7.82,0L16,10.24l2.53-2.58A5.44,5.44,0,0,1,22.45,6m0-2a7.47,7.47,0,0,0-5.34,2.24L16,7.36,14.89,6.24a7.49,7.49,0,0,0-10.68,0,7.72,7.72,0,0,0,0,10.82L16,29,27.79,17.06a7.72,7.72,0,0,0,0-10.82A7.49,7.49,0,0,0,22.45,4Z\"></path></svg>\n", "\t\t\t\t<span class=\"text-white\">148</span></div>\n", "\n", "\t\t<main class=\"px-3 flex flex-col justify-center h-full pt-0.5 relative\"><div class=\"mb-0.5 flex items-center justify-start gap-1.5 font-semibold leading-tight text-white text-sm\"><h4 class=\"line-clamp-2 text-balance text-left drop-shadow-lg\">Privacy-Safe Synthetic Data Generation | Syncora AI</h4>\n", "\t\t\t\t<div class=\"leading-none drop-shadow-2xl\">🐠</div></div>\n", "\t\t\t<p class=\"line-clamp-1 text-left text-[0.8375rem]/[1.15rem] text-gray-200 opacity-85 drop-shadow-sm\">Privacy-safe synthetic data for ML and data augmentation</p></main>\n", "\t\t</a></article></div>\n", "\t\t\t\t<a href=\"/spaces\" class=\"self-center underline decoration-gray-300 underline-offset-8 transition-all hover:decoration-gray-800 hover:underline-offset-[6px] dark:text-gray-300 dark:decoration-gray-600 dark:hover:decoration-gray-300 max-sm:mb-5\">Browse 400k+ applications</a></div>\n", "\t\t\t<div class=\"relative col-span-1 flex flex-col items-stretch text-center\"><h2 class=\"mb-5 flex items-center justify-center gap-2 text-lg font-semibold 2xl:mb-6 2xl:text-xl\"><svg class=\"text-gray-500\" style=\"\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" aria-hidden=\"true\" focusable=\"false\" role=\"img\" width=\"1em\" height=\"1em\" preserveAspectRatio=\"xMidYMid meet\" viewBox=\"0 0 25 25\"><ellipse cx=\"12.5\" cy=\"5\" fill=\"currentColor\" fill-opacity=\"0.25\" rx=\"7.5\" ry=\"2\"></ellipse><path d=\"M12.5 15C16.6421 15 20 14.1046 20 13V20C20 21.1046 16.6421 22 12.5 22C8.35786 22 5 21.1046 5 20V13C5 14.1046 8.35786 15 12.5 15Z\" fill=\"currentColor\" opacity=\"0.5\"></path><path d=\"M12.5 7C16.6421 7 20 6.10457 20 5V11.5C20 12.6046 16.6421 13.5 12.5 13.5C8.35786 13.5 5 12.6046 5 11.5V5C5 6.10457 8.35786 7 12.5 7Z\" fill=\"currentColor\" opacity=\"0.5\"></path><path d=\"M5.23628 12C5.08204 12.1598 5 12.8273 5 13C5 14.1046 8.35786 15 12.5 15C16.6421 15 20 14.1046 20 13C20 12.8273 19.918 12.1598 19.7637 12C18.9311 12.8626 15.9947 13.5 12.5 13.5C9.0053 13.5 6.06886 12.8626 5.23628 12Z\" fill=\"currentColor\"></path></svg>\n", "\t\t\t\t\tDatasets\n", "\t\t\t\t</h2>\n", "\t\t\t\t<div class=\"mb-3 flex flex-col gap-2.5 rounded-xl bg-white/40 p-3 backdrop-blur-lg dark:bg-gray-900/40 sm:mb-7\"><article class=\"overview-card-wrapper group/repo white \"><a class=\"flex items-center justify-between gap-4 p-2\" href=\"/datasets/syncora/developer-productivity-simulated-behavioral-data\"><div class=\"w-full truncate\"><header class=\"flex items-center mb-0.5\" title=\"syncora/developer-productivity-simulated-behavioral-data\">\n", "\t\t\t\t<h4 class=\"text-md truncate font-mono text-black dark:group-hover/repo:text-yellow-500 group-hover/repo:text-red-600 text-smd\">syncora/developer-productivity-simulated-behavioral-data</h4>\n", "\t\t\t\t\n", "\t\t\t\t</header>\n", "\t\t\t<div class=\"mr-1 flex items-center overflow-hidden whitespace-nowrap text-sm leading-tight text-gray-400\">\n", "\t\t\t\t<span class=\"truncate\">Updated\n", "\t\t\t\t\t<time datetime=\"2025-08-26T09:42:37\" title=\"<PERSON><PERSON>, 26 Aug 2025 09:42:37 GMT\">4 days ago</time></span>\n", "\t\t\t\t\n", "\t\t\t\t<span class=\"px-1.5 text-gray-300 dark:text-gray-500\">• </span>\n", "\t\t\t\t\t<svg class=\"flex-none w-3 text-gray-400 mr-0.5\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" aria-hidden=\"true\" focusable=\"false\" role=\"img\" width=\"1em\" height=\"1em\" viewBox=\"0 0 32 32\"><path fill=\"currentColor\" d=\"M26 24v4H6v-4H4v4a2 2 0 0 0 2 2h20a2 2 0 0 0 2-2v-4zm0-10l-1.41-1.41L17 20.17V2h-2v18.17l-7.59-7.58L6 14l10 10l10-10z\"></path></svg>\n", "\t\t\t\t\t141\n", "\t\t\t\t\n", "\t\t\t\t<span class=\"px-1.5 text-gray-300 dark:text-gray-500\">• </span>\n", "\t\t\t\t\t<svg class=\"flex-none w-3 text-gray-400 mr-1\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" aria-hidden=\"true\" focusable=\"false\" role=\"img\" width=\"1em\" height=\"1em\" preserveAspectRatio=\"xMidYMid meet\" viewBox=\"0 0 32 32\" fill=\"currentColor\"><path d=\"M22.45,6a5.47,5.47,0,0,1,3.91,1.64,5.7,5.7,0,0,1,0,8L16,26.13,5.64,15.64a5.7,5.7,0,0,1,0-8,5.48,5.48,0,0,1,7.82,0L16,10.24l2.53-2.58A5.44,5.44,0,0,1,22.45,6m0-2a7.47,7.47,0,0,0-5.34,2.24L16,7.36,14.89,6.24a7.49,7.49,0,0,0-10.68,0,7.72,7.72,0,0,0,0,10.82L16,29,27.79,17.06a7.72,7.72,0,0,0,0-10.82A7.49,7.49,0,0,0,22.45,4Z\"></path></svg>\n", "\t\t\t\t\t142\n", "\n", "\t\t\t\t</div></div>\n", "\t\t</a></article><article class=\"overview-card-wrapper group/repo white \"><a class=\"flex items-center justify-between gap-4 p-2\" href=\"/datasets/fka/awesome-chatgpt-prompts\"><div class=\"w-full truncate\"><header class=\"flex items-center mb-0.5\" title=\"fka/awesome-chatgpt-prompts\">\n", "\t\t\t\t<h4 class=\"text-md truncate font-mono text-black dark:group-hover/repo:text-yellow-500 group-hover/repo:text-red-600 text-smd\">fka/awesome-chatgpt-prompts</h4>\n", "\t\t\t\t\n", "\t\t\t\t</header>\n", "\t\t\t<div class=\"mr-1 flex items-center overflow-hidden whitespace-nowrap text-sm leading-tight text-gray-400\">\n", "\t\t\t\t<span class=\"truncate\">Updated\n", "\t\t\t\t\t<time datetime=\"2025-01-06T00:02:53\" title=\"Mon, 06 Jan 2025 00:02:53 GMT\">Jan 6</time></span>\n", "\t\t\t\t\n", "\t\t\t\t<span class=\"px-1.5 text-gray-300 dark:text-gray-500\">• </span>\n", "\t\t\t\t\t<svg class=\"flex-none w-3 text-gray-400 mr-0.5\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" aria-hidden=\"true\" focusable=\"false\" role=\"img\" width=\"1em\" height=\"1em\" viewBox=\"0 0 32 32\"><path fill=\"currentColor\" d=\"M26 24v4H6v-4H4v4a2 2 0 0 0 2 2h20a2 2 0 0 0 2-2v-4zm0-10l-1.41-1.41L17 20.17V2h-2v18.17l-7.59-7.58L6 14l10 10l10-10z\"></path></svg>\n", "\t\t\t\t\t41.1k\n", "\t\t\t\t\n", "\t\t\t\t<span class=\"px-1.5 text-gray-300 dark:text-gray-500\">• </span>\n", "\t\t\t\t\t<svg class=\"flex-none w-3 text-gray-400 mr-1\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" aria-hidden=\"true\" focusable=\"false\" role=\"img\" width=\"1em\" height=\"1em\" preserveAspectRatio=\"xMidYMid meet\" viewBox=\"0 0 32 32\" fill=\"currentColor\"><path d=\"M22.45,6a5.47,5.47,0,0,1,3.91,1.64,5.7,5.7,0,0,1,0,8L16,26.13,5.64,15.64a5.7,5.7,0,0,1,0-8,5.48,5.48,0,0,1,7.82,0L16,10.24l2.53-2.58A5.44,5.44,0,0,1,22.45,6m0-2a7.47,7.47,0,0,0-5.34,2.24L16,7.36,14.89,6.24a7.49,7.49,0,0,0-10.68,0,7.72,7.72,0,0,0,0,10.82L16,29,27.79,17.06a7.72,7.72,0,0,0,0-10.82A7.49,7.49,0,0,0,22.45,4Z\"></path></svg>\n", "\t\t\t\t\t8.91k\n", "\n", "\t\t\t\t</div></div>\n", "\t\t</a></article><article class=\"overview-card-wrapper group/repo white \"><a class=\"flex items-center justify-between gap-4 p-2\" href=\"/datasets/syncora/synthetic-healthcare-admissions\"><div class=\"w-full truncate\"><header class=\"flex items-center mb-0.5\" title=\"syncora/synthetic-healthcare-admissions\">\n", "\t\t\t\t<h4 class=\"text-md truncate font-mono text-black dark:group-hover/repo:text-yellow-500 group-hover/repo:text-red-600 text-smd\">syncora/synthetic-healthcare-admissions</h4>\n", "\t\t\t\t\n", "\t\t\t\t</header>\n", "\t\t\t<div class=\"mr-1 flex items-center overflow-hidden whitespace-nowrap text-sm leading-tight text-gray-400\">\n", "\t\t\t\t<span class=\"truncate\">Updated\n", "\t\t\t\t\t<time datetime=\"2025-08-26T10:15:04\" title=\"<PERSON><PERSON>, 26 Aug 2025 10:15:04 GMT\">4 days ago</time></span>\n", "\t\t\t\t\n", "\t\t\t\t<span class=\"px-1.5 text-gray-300 dark:text-gray-500\">• </span>\n", "\t\t\t\t\t<svg class=\"flex-none w-3 text-gray-400 mr-0.5\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" aria-hidden=\"true\" focusable=\"false\" role=\"img\" width=\"1em\" height=\"1em\" viewBox=\"0 0 32 32\"><path fill=\"currentColor\" d=\"M26 24v4H6v-4H4v4a2 2 0 0 0 2 2h20a2 2 0 0 0 2-2v-4zm0-10l-1.41-1.41L17 20.17V2h-2v18.17l-7.59-7.58L6 14l10 10l10-10z\"></path></svg>\n", "\t\t\t\t\t40\n", "\t\t\t\t\n", "\t\t\t\t<span class=\"px-1.5 text-gray-300 dark:text-gray-500\">• </span>\n", "\t\t\t\t\t<svg class=\"flex-none w-3 text-gray-400 mr-1\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" aria-hidden=\"true\" focusable=\"false\" role=\"img\" width=\"1em\" height=\"1em\" preserveAspectRatio=\"xMidYMid meet\" viewBox=\"0 0 32 32\" fill=\"currentColor\"><path d=\"M22.45,6a5.47,5.47,0,0,1,3.91,1.64,5.7,5.7,0,0,1,0,8L16,26.13,5.64,15.64a5.7,5.7,0,0,1,0-8,5.48,5.48,0,0,1,7.82,0L16,10.24l2.53-2.58A5.44,5.44,0,0,1,22.45,6m0-2a7.47,7.47,0,0,0-5.34,2.24L16,7.36,14.89,6.24a7.49,7.49,0,0,0-10.68,0,7.72,7.72,0,0,0,0,10.82L16,29,27.79,17.06a7.72,7.72,0,0,0,0-10.82A7.49,7.49,0,0,0,22.45,4Z\"></path></svg>\n", "\t\t\t\t\t65\n", "\n", "\t\t\t\t</div></div>\n", "\t\t</a></article><article class=\"overview-card-wrapper group/repo white \"><a class=\"flex items-center justify-between gap-4 p-2\" href=\"/datasets/openai/healthbench\"><div class=\"w-full truncate\"><header class=\"flex items-center mb-0.5\" title=\"openai/healthbench\">\n", "\t\t\t\t<h4 class=\"text-md truncate font-mono text-black dark:group-hover/repo:text-yellow-500 group-hover/repo:text-red-600 text-smd\">openai/healthbench</h4>\n", "\t\t\t\t\n", "\t\t\t\t</header>\n", "\t\t\t<div class=\"mr-1 flex items-center overflow-hidden whitespace-nowrap text-sm leading-tight text-gray-400\">\n", "\t\t\t\t<span class=\"truncate\">Updated\n", "\t\t\t\t\t<time datetime=\"2025-08-27T15:58:59\" title=\"Wed, 27 Aug 2025 15:58:59 GMT\">3 days ago</time></span>\n", "\t\t\t\t\n", "\t\t\t\t<span class=\"px-1.5 text-gray-300 dark:text-gray-500\">• </span>\n", "\t\t\t\t\t<svg class=\"flex-none w-3 text-gray-400 mr-0.5\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" aria-hidden=\"true\" focusable=\"false\" role=\"img\" width=\"1em\" height=\"1em\" viewBox=\"0 0 32 32\"><path fill=\"currentColor\" d=\"M26 24v4H6v-4H4v4a2 2 0 0 0 2 2h20a2 2 0 0 0 2-2v-4zm0-10l-1.41-1.41L17 20.17V2h-2v18.17l-7.59-7.58L6 14l10 10l10-10z\"></path></svg>\n", "\t\t\t\t\t255\n", "\t\t\t\t\n", "\t\t\t\t<span class=\"px-1.5 text-gray-300 dark:text-gray-500\">• </span>\n", "\t\t\t\t\t<svg class=\"flex-none w-3 text-gray-400 mr-1\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" aria-hidden=\"true\" focusable=\"false\" role=\"img\" width=\"1em\" height=\"1em\" preserveAspectRatio=\"xMidYMid meet\" viewBox=\"0 0 32 32\" fill=\"currentColor\"><path d=\"M22.45,6a5.47,5.47,0,0,1,3.91,1.64,5.7,5.7,0,0,1,0,8L16,26.13,5.64,15.64a5.7,5.7,0,0,1,0-8,5.48,5.48,0,0,1,7.82,0L16,10.24l2.53-2.58A5.44,5.44,0,0,1,22.45,6m0-2a7.47,7.47,0,0,0-5.34,2.24L16,7.36,14.89,6.24a7.49,7.49,0,0,0-10.68,0,7.72,7.72,0,0,0,0,10.82L16,29,27.79,17.06a7.72,7.72,0,0,0,0-10.82A7.49,7.49,0,0,0,22.45,4Z\"></path></svg>\n", "\t\t\t\t\t56\n", "\n", "\t\t\t\t</div></div>\n", "\t\t</a></article><article class=\"overview-card-wrapper group/repo white \"><a class=\"flex items-center justify-between gap-4 p-2\" href=\"/datasets/nvidia/Nemotron-Post-Training-Dataset-v2\"><div class=\"w-full truncate\"><header class=\"flex items-center mb-0.5\" title=\"nvidia/Nemotron-Post-Training-Dataset-v2\">\n", "\t\t\t\t<h4 class=\"text-md truncate font-mono text-black dark:group-hover/repo:text-yellow-500 group-hover/repo:text-red-600 text-smd\">nvidia/Nemotron-Post-Training-Dataset-v2</h4>\n", "\t\t\t\t\n", "\t\t\t\t</header>\n", "\t\t\t<div class=\"mr-1 flex items-center overflow-hidden whitespace-nowrap text-sm leading-tight text-gray-400\">\n", "\t\t\t\t<span class=\"truncate\">Updated\n", "\t\t\t\t\t<time datetime=\"2025-08-21T04:29:18\" title=\"Thu, 21 Aug 2025 04:29:18 GMT\">9 days ago</time></span>\n", "\t\t\t\t\n", "\t\t\t\t<span class=\"px-1.5 text-gray-300 dark:text-gray-500\">• </span>\n", "\t\t\t\t\t<svg class=\"flex-none w-3 text-gray-400 mr-0.5\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" aria-hidden=\"true\" focusable=\"false\" role=\"img\" width=\"1em\" height=\"1em\" viewBox=\"0 0 32 32\"><path fill=\"currentColor\" d=\"M26 24v4H6v-4H4v4a2 2 0 0 0 2 2h20a2 2 0 0 0 2-2v-4zm0-10l-1.41-1.41L17 20.17V2h-2v18.17l-7.59-7.58L6 14l10 10l10-10z\"></path></svg>\n", "\t\t\t\t\t2.49k\n", "\t\t\t\t\n", "\t\t\t\t<span class=\"px-1.5 text-gray-300 dark:text-gray-500\">• </span>\n", "\t\t\t\t\t<svg class=\"flex-none w-3 text-gray-400 mr-1\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" aria-hidden=\"true\" focusable=\"false\" role=\"img\" width=\"1em\" height=\"1em\" preserveAspectRatio=\"xMidYMid meet\" viewBox=\"0 0 32 32\" fill=\"currentColor\"><path d=\"M22.45,6a5.47,5.47,0,0,1,3.91,1.64,5.7,5.7,0,0,1,0,8L16,26.13,5.64,15.64a5.7,5.7,0,0,1,0-8,5.48,5.48,0,0,1,7.82,0L16,10.24l2.53-2.58A5.44,5.44,0,0,1,22.45,6m0-2a7.47,7.47,0,0,0-5.34,2.24L16,7.36,14.89,6.24a7.49,7.49,0,0,0-10.68,0,7.72,7.72,0,0,0,0,10.82L16,29,27.79,17.06a7.72,7.72,0,0,0,0-10.82A7.49,7.49,0,0,0,22.45,4Z\"></path></svg>\n", "\t\t\t\t\t44\n", "\n", "\t\t\t\t</div></div>\n", "\t\t</a></article></div>\n", "\t\t\t\t<a href=\"/datasets\" class=\"self-center underline decoration-gray-300 underline-offset-8 transition-all hover:decoration-gray-800 hover:underline-offset-[6px] dark:text-gray-300 dark:decoration-gray-600 dark:hover:decoration-gray-300 max-sm:mb-5\">Browse 250k+ datasets</a></div></div></div>\n", "\n", "\t<div class=\"container\"><div class=\"bg-linear-to-b mb-28 flex h-48 w-full flex-col items-stretch rounded-t-[50%] from-gray-100/60 via-white to-white pt-24 text-center dark:from-gray-900/60 dark:via-gray-950 dark:to-gray-950 2xl:mb-32\"><svg class=\"text-3xl 2xl:text-4xl self-center text-gray-900 dark:text-gray-500 mb-2 sm:mb-4 flex-none\" style=\"\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" aria-hidden=\"true\" focusable=\"false\" role=\"img\" width=\"1em\" height=\"1em\" preserveAspectRatio=\"xMidYMid meet\" viewBox=\"0 0 24 24\"><path class=\"uim-quaternary\" d=\"M20.23 7.24L12 12L3.77 7.24a1.98 1.98 0 0 1 .7-.71L11 2.76c.62-.35 1.38-.35 2 0l6.53 3.77c.29.173.531.418.7.71z\" opacity=\".25\" fill=\"currentColor\"></path><path class=\"uim-tertiary\" d=\"M12 12v9.5a2.09 2.09 0 0 1-.91-.21L4.5 17.48a2.003 2.003 0 0 1-1-1.73v-7.5a2.06 2.06 0 0 1 .27-1.01L12 12z\" opacity=\".5\" fill=\"currentColor\"></path><path class=\"uim-primary\" d=\"M20.5 8.25v7.5a2.003 2.003 0 0 1-1 1.73l-6.62 3.82c-.275.13-.576.198-.88.2V12l8.23-4.76c.175.308.268.656.27 1.01z\" fill=\"currentColor\"></path></svg>\n", "\n", "\t\t\t<div class=\"mb-2 flex flex-none items-center justify-center gap-2 text-xl font-bold\"><div class=\"bg-linear-to-l mr-4 h-px flex-1 from-gray-100 to-white dark:from-gray-800 dark:to-gray-950\"></div>\n", "\t\t\t\t<h2 class=\"text-balance text-3xl font-bold 2xl:text-4xl\">The Home of Machine Learning</h2>\n", "\t\t\t\t<div class=\"bg-linear-to-r ml-4 h-px flex-1 from-gray-100 to-white dark:from-gray-800 dark:to-gray-950\"></div></div>\n", "\n", "\t\t\t<p class=\"flex-none text-balance text-lg text-gray-500 2xl:text-xl\">Create, discover and collaborate on ML better.\n", "\t\t\t</p></div>\n", "\n", "\t\t<div class=\"mx-auto grid max-w-5xl gap-4 md:gap-8 lg:grid-cols-3 2xl:max-w-7xl\"><div class=\"shadow-xs relative col-span-1 flex h-[400px] flex-col overflow-hidden rounded-xl border bg-white sm:h-[420px] lg:col-span-2 2xl:h-[500px]\"><div class=\"p-6 md:p-8\"><h3 class=\"text-xl font-semibold 2xl:text-2xl\">The collaboration platform</h3>\n", "\t\t\t\t\t<p class=\"text-base text-gray-500 2xl:text-lg\">Host and collaborate on unlimited public models, datasets and applications.\n", "\t\t\t\t\t</p></div>\n", "\t\t\t\t<img src=\"/front/assets/homepage/activity.svg\" class=\"ml-auto w-full flex-1 overflow-hidden border-t border-gray-100 object-cover object-left-top dark:hidden\" alt=\"Hub activity feed\">\n", "\t\t\t\t<img src=\"/front/assets/homepage/activity-dark.svg\" class=\"ml-auto hidden w-full flex-1 overflow-hidden border-t border-gray-100 object-cover object-left-top dark:block\" alt=\"Hub activity feed\">\n", "\t\t\t\t<div class=\"bg-linear-to-tl pointer-events-none absolute inset-x-0 bottom-0 h-40 select-none from-white/80 to-white/0 dark:from-gray-950/80 dark:to-gray-950/0\"></div></div>\n", "\t\t\t<div class=\"shadow-xs relative col-span-1 flex h-[390px] flex-col overflow-hidden rounded-xl border bg-white sm:h-[420px] 2xl:h-[500px]\"><div class=\"p-6 md:p-8\"><h3 class=\"text-xl font-semibold 2xl:text-2xl\">Move faster</h3>\n", "\t\t\t\t\t<p class=\"text-base text-gray-500 2xl:text-lg\">With the HF Open source stack.</p></div>\n", "\t\t\t\t<img src=\"/front/assets/homepage/snippets.svg\" class=\"ml-auto w-full flex-1 overflow-hidden border-t border-gray-100 object-cover object-left-top dark:hidden\" alt=\"Code snippets from HF libraries\">\n", "\t\t\t\t<img src=\"/front/assets/homepage/snippets-dark.svg\" class=\"ml-auto hidden w-full flex-1 overflow-hidden border-t border-gray-100 object-cover object-left-top dark:block\" alt=\"Code snippets from HF libraries\">\n", "\t\t\t\t<div class=\"bg-linear-to-tl pointer-events-none absolute inset-x-0 bottom-0 h-40 select-none from-white/80 to-white/0 dark:from-gray-950/80 dark:to-gray-950/0\"></div></div>\n", "\t\t\t<div class=\"shadow-xs relative col-span-1 flex h-[390px] flex-col overflow-hidden rounded-xl border bg-white sm:h-[420px] 2xl:h-[500px]\"><div class=\"p-6 md:p-8\"><h3 class=\"text-xl font-semibold 2xl:text-2xl\">Explore all modalities</h3>\n", "\t\t\t\t\t<p class=\"text-base text-gray-500 2xl:text-lg\">Text, image, video, audio or even 3D.</p></div>\n", "\t\t\t\t<img src=\"/front/assets/homepage/modalities.svg\" class=\"ml-auto w-full overflow-hidden border-t border-gray-100 bg-gray-50 object-cover dark:hidden\" alt=\"Hugging face tasks \">\n", "\t\t\t\t<img src=\"/front/assets/homepage/modalities-dark.svg\" class=\"ml-auto hidden w-full overflow-hidden border-t border-gray-100 bg-gray-950 object-cover dark:block\" alt=\"Hugging face tasks \"></div>\n", "\t\t\t<div class=\"shadow-xs relative col-span-1 flex h-[390px] flex-col overflow-hidden rounded-xl border bg-white sm:h-[420px] lg:col-span-2 2xl:h-[500px]\"><div class=\"flex justify-between p-6 md:p-8\"><div><h3 class=\"text-xl font-semibold 2xl:text-2xl\">Build your portfolio</h3>\n", "\t\t\t\t\t\t<p class=\"text-base text-gray-500 2xl:text-lg\">Share your work with the world and build your ML profile.</p></div>\n", "\t\t\t\t\t<a class=\"text-smd flex-none self-start rounded-full border border-transparent bg-gray-900 px-2.5 py-1.5 leading-none text-white hover:border-black hover:bg-white hover:text-black\" href=\"/join\">Sign Up\n", "\t\t\t\t\t</a></div>\n", "\t\t\t\t<img src=\"/front/assets/homepage/younes.svg\" class=\"ml-auto w-full flex-1 overflow-hidden border-t border-gray-100 object-cover object-left-top dark:hidden\" alt=\"Younes Belkada Hugging Face profile\">\n", "\t\t\t\t<img src=\"/front/assets/homepage/younes-dark.svg\" class=\"ml-auto hidden w-full flex-1 overflow-hidden border-t border-gray-100 object-cover object-left-top dark:block\" alt=\"Youne Belkada Hugging Face profile\">\n", "\t\t\t\t<div class=\"bg-linear-to-tl pointer-events-none absolute inset-x-0 bottom-0 h-40 select-none from-white/80 to-white/0 dark:from-gray-950/80 dark:to-gray-950/0\"></div></div></div>\n", "\n", "\t\t<div class=\"flex w-full flex-col items-stretch pb-24 pt-24 text-center\"><div class=\"mb-2 flex items-center justify-center gap-2 text-xl font-bold\"><div class=\"bg-linear-to-l mr-4 h-px flex-1 from-gray-100 to-white dark:from-gray-800 dark:to-gray-950\"></div>\n", "\t\t\t\t<h2 class=\"text-3xl font-bold 2xl:text-4xl\">Accelerate your ML</h2>\n", "\t\t\t\t<div class=\"bg-linear-to-r ml-4 h-px flex-1 from-gray-100 to-white dark:from-gray-800 dark:to-gray-950\"></div></div>\n", "\n", "\t\t\t<p class=\"text-balance text-lg text-gray-500 2xl:text-xl\">We provide paid Compute and Enterprise solutions.</p></div>\n", "\t\t<div class=\"mx-auto grid max-w-5xl grid-cols-1 gap-10 2xl:max-w-7xl\"><div class=\"shadow-xs col-span-1 grid h-[450px] grid-cols-1 items-center overflow-hidden rounded-xl border bg-white lg:h-[400px] lg:grid-cols-3\"><div class=\"col-span-1 p-7 md:p-10\"><h3 class=\"mb-4 text-2xl font-semibold\">Compute</h3>\n", "\t\t\t\t\t<p class=\"mb-6 text-balance text-base text-gray-500 lg:mb-8 2xl:text-lg\">Deploy on optimized <a class=\"underline\" href=\"/pricing#endpoints\">Inference Endpoints</a> or update your\n", "\t\t\t\t\t\t<a class=\"underline\" href=\"/pricing#spaces\">Spaces applications</a> to a GPU in a few clicks.\n", "\t\t\t\t\t</p>\n", "\t\t\t\t\t<div class=\"flex items-center gap-x-2.5 whitespace-nowrap sm:gap-x-3.5\"><a class=\"leading-black rounded-lg border border-transparent bg-black px-3.5 py-1 text-base text-white hover:border-black hover:bg-white hover:text-gray-900 2xl:text-lg\" href=\"/pricing\">View pricing\n", "\t\t\t\t\t\t</a>\n", "\t\t\t\t\t\t<p class=\"text-smd text-gray-400 2xl:text-base\">Starting at $0.60/hour for GPU</p></div></div>\n", "\t\t\t\t<div class=\"col-span-1 self-stretch lg:col-span-2\"><img src=\"/front/assets/homepage/compute.svg\" alt=\"Hugging face compute offering\" class=\"pointer-events-none h-full w-full select-none bg-bottom object-cover object-left dark:hidden\">\n", "\t\t\t\t\t<img src=\"/front/assets/homepage/compute-dark.svg\" alt=\"Hugging face compute offering\" class=\"pointer-events-none hidden h-full w-full select-none bg-bottom object-cover object-left dark:block\"></div></div>\n", "\t\t\t<div class=\"bg-linear-to-b shadow-xs relative col-span-1 grid h-[450px] grid-cols-1 items-center rounded-xl from-gray-900 to-gray-950 text-gray-200 lg:h-[400px] lg:grid-cols-3\"><div class=\"col-span-1 p-7 md:p-10\"><h3 class=\"mb-4 text-2xl font-semibold\">Team &amp; Enterprise</h3>\n", "\t\t<p class=\"mb-6 text-base text-gray-500 lg:mb-8 2xl:text-lg\" style=\"text-wrap: balance;\">Give your team the most advanced platform to build AI with enterprise-grade security, access controls and\n", "\t\t\tdedicated support.\n", "\t\t</p>\n", "\t\t<div class=\"flex items-center gap-x-2.5 whitespace-nowrap sm:gap-x-3.5\"><a class=\"leading-black rounded-lg border border-transparent bg-gray-200 px-3.5 py-1 text-base text-gray-900 hover:border-white hover:bg-black hover:text-white dark:bg-black dark:hover:bg-white dark:hover:text-gray-900 2xl:text-lg\" href=\"/enterprise\">Getting started\n", "\t\t\t</a>\n", "\t\t\t<p class=\"text-smd text-gray-400 2xl:text-base\">Starting at $20/user/month</p></div></div>\n", "\t<div class=\"col-span-1 self-stretch rounded-r-xl lg:relative lg:col-span-2 lg:ml-8\"><div class=\"absolute right-20 top-8 hidden h-[300px] w-full max-w-2xl lg:block\"><a href=\"/enterprise\" class=\"top-16 left-24 backdrop-blur-xs absolute z-2 rounded-lg border border-white/10 bg-white/10 px-4 py-2 text-sm font-semibold text-white transition-all duration-200 hover:bg-white/15\">Single Sign-On</a><a href=\"/enterprise\" class=\"left-1/2 -translate-x-1/2 backdrop-blur-xs absolute z-2 rounded-lg border border-white/10 bg-white/10 px-4 py-2 text-sm font-semibold text-white transition-all duration-200 hover:bg-white/15\">Regions</a><a href=\"/enterprise\" class=\"right-10 bottom-16 backdrop-blur-xs absolute z-2 rounded-lg border border-white/10 bg-white/10 px-4 py-2 text-sm font-semibold text-white transition-all duration-200 hover:bg-white/15\">Priority Support</a><a href=\"/enterprise\" class=\"left-10 xl:left-6 bottom-1/3 backdrop-blur-xs absolute z-2 rounded-lg border border-white/10 bg-white/10 px-4 py-2 text-sm font-semibold text-white transition-all duration-200 hover:bg-white/15\">Audit Logs</a><a href=\"/enterprise\" class=\"bottom-0 right-1/2 backdrop-blur-xs absolute z-2 rounded-lg border border-white/10 bg-white/10 px-4 py-2 text-sm font-semibold text-white transition-all duration-200 hover:bg-white/15\">Resource Groups</a><a href=\"/enterprise\" class=\"right-0 top-20 backdrop-blur-xs absolute z-2 rounded-lg border border-white/10 bg-white/10 px-4 py-2 text-sm font-semibold text-white transition-all duration-200 hover:bg-white/15\">Private Datasets Viewer</a></div>\n", "\t\t<div class=\"pointer-events-none absolute right-0 top-0 hidden h-64 w-64 overflow-hidden rounded-r-xl lg:block\"><div class=\"-translate-y-20 translate-x-16\"><svg class=\"w-full h-full animate__bounce pointer-events-none [animation-duration:11s]\" style=\"--rotation-angle: -30deg;\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" aria-hidden=\"true\" focusable=\"false\" role=\"img\" width=\"1em\" height=\"1em\" preserveAspectRatio=\"xMidYMid meet\" viewBox=\"0 0 24 24\"><path class=\"uim-quaternary fill-[#4A4E57]\" d=\"M20.23 7.24L12 12L3.77 7.24a1.98 1.98 0 0 1 .7-.71L11 2.76c.62-.35 1.38-.35 2 0l6.53 3.77c.29.173.531.418.7.71z\"></path><path class=\"uim-tertiary fill-[#86898F]\" d=\"M12 12v9.5a2.09 2.09 0 0 1-.91-.21L4.5 17.48a2.003 2.003 0 0 1-1-1.73v-7.5a2.06 2.06 0 0 1 .27-1.01L12 12z\"></path><path class=\"uim-primary fill-white\" d=\"M20.5 8.25v7.5a2.003 2.003 0 0 1-1 1.73l-6.62 3.82c-.275.13-.576.198-.88.2V12l8.23-4.76c.175.308.268.656.27 1.01z\"></path></svg></div></div>\n", "\t\t<svg class=\"absolute text-8xl z-1 -top-8 right-32 lg:left-32 animate__bounce pointer-events-none [animation-duration:12s]\" style=\"--rotation-angle: 16deg;\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" aria-hidden=\"true\" focusable=\"false\" role=\"img\" width=\"1em\" height=\"1em\" preserveAspectRatio=\"xMidYMid meet\" viewBox=\"0 0 24 24\"><path class=\"uim-quaternary fill-[#4A4E57]\" d=\"M20.23 7.24L12 12L3.77 7.24a1.98 1.98 0 0 1 .7-.71L11 2.76c.62-.35 1.38-.35 2 0l6.53 3.77c.29.173.531.418.7.71z\"></path><path class=\"uim-tertiary fill-[#86898F]\" d=\"M12 12v9.5a2.09 2.09 0 0 1-.91-.21L4.5 17.48a2.003 2.003 0 0 1-1-1.73v-7.5a2.06 2.06 0 0 1 .27-1.01L12 12z\"></path><path class=\"uim-primary fill-white\" d=\"M20.5 8.25v7.5a2.003 2.003 0 0 1-1 1.73l-6.62 3.82c-.275.13-.576.198-.88.2V12l8.23-4.76c.175.308.268.656.27 1.01z\"></path></svg>\n", "\t\t<svg class=\"absolute w-36 h-36 lg:w-44 lg:h-44 z-1 bottom-6 left-8 sm:left-16 animate__bounce pointer-events-none [animation-duration:9s]\" style=\"--rotation-angle: -75deg;\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" aria-hidden=\"true\" focusable=\"false\" role=\"img\" width=\"1em\" height=\"1em\" preserveAspectRatio=\"xMidYMid meet\" viewBox=\"0 0 24 24\"><path class=\"uim-quaternary fill-[#4A4E57]\" d=\"M20.23 7.24L12 12L3.77 7.24a1.98 1.98 0 0 1 .7-.71L11 2.76c.62-.35 1.38-.35 2 0l6.53 3.77c.29.173.531.418.7.71z\"></path><path class=\"uim-tertiary fill-[#86898F]\" d=\"M12 12v9.5a2.09 2.09 0 0 1-.91-.21L4.5 17.48a2.003 2.003 0 0 1-1-1.73v-7.5a2.06 2.06 0 0 1 .27-1.01L12 12z\"></path><path class=\"uim-primary fill-white\" d=\"M20.5 8.25v7.5a2.003 2.003 0 0 1-1 1.73l-6.62 3.82c-.275.13-.576.198-.88.2V12l8.23-4.76c.175.308.268.656.27 1.01z\"></path></svg>\n", "\t\t<svg class=\"absolute text-6xl z-1 left-1/2 top-20 animate__bounce pointer-events-none [animation-duration:7s] blur-[1px] hidden lg:block\" style=\"--rotation-angle: 35deg;\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" aria-hidden=\"true\" focusable=\"false\" role=\"img\" width=\"1em\" height=\"1em\" preserveAspectRatio=\"xMidYMid meet\" viewBox=\"0 0 24 24\"><path class=\"uim-quaternary fill-[#4A4E57]\" d=\"M20.23 7.24L12 12L3.77 7.24a1.98 1.98 0 0 1 .7-.71L11 2.76c.62-.35 1.38-.35 2 0l6.53 3.77c.29.173.531.418.7.71z\"></path><path class=\"uim-tertiary fill-[#86898F]\" d=\"M12 12v9.5a2.09 2.09 0 0 1-.91-.21L4.5 17.48a2.003 2.003 0 0 1-1-1.73v-7.5a2.06 2.06 0 0 1 .27-1.01L12 12z\"></path><path class=\"uim-primary fill-white\" d=\"M20.5 8.25v7.5a2.003 2.003 0 0 1-1 1.73l-6.62 3.82c-.275.13-.576.198-.88.2V12l8.23-4.76c.175.308.268.656.27 1.01z\"></path></svg>\n", "\t\t<svg class=\"absolute text-6xl z-1 left-1/3 top-32 animate__bounce pointer-events-none [animation-duration:13s] blur-[1px] hidden lg:block\" style=\"--rotation-angle: 28deg;\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" aria-hidden=\"true\" focusable=\"false\" role=\"img\" width=\"1em\" height=\"1em\" preserveAspectRatio=\"xMidYMid meet\" viewBox=\"0 0 25 25\"><ellipse cx=\"12.5\" cy=\"5\" class=\"fill-[#4A4E57]\" rx=\"7.5\" ry=\"2\"></ellipse><path d=\"M12.5 15C16.6421 15 20 14.1046 20 13V20C20 21.1046 16.6421 22 12.5 22C8.35786 22 5 21.1046 5 20V13C5 14.1046 8.35786 15 12.5 15Z\" class=\"fill-[#86898F]\"></path><path d=\"M12.5 7C16.6421 7 20 6.10457 20 5V11.5C20 12.6046 16.6421 13.5 12.5 13.5C8.35786 13.5 5 12.6046 5 11.5V5C5 6.10457 8.35786 7 12.5 7Z\" class=\"fill-[#86898F]\"></path><path d=\"M5.23628 12C5.08204 12.1598 5 12.8273 5 13C5 14.1046 8.35786 15 12.5 15C16.6421 15 20 14.1046 20 13C20 12.8273 19.918 12.1598 19.7637 12C18.9311 12.8626 15.9947 13.5 12.5 13.5C9.0053 13.5 6.06886 12.8626 5.23628 12Z\" class=\"fill-white\"></path></svg>\n", "\t\t<svg class=\"absolute w-44 h-44 lg:w-56 lg:h-56 z-1 -bottom-10 lg:-bottom-16 right-10 lg:right-1/4 sm:right-32 animate__bounce pointer-events-none [animation-duration:11s]\" style=\"--rotation-angle: -36deg;\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" aria-hidden=\"true\" focusable=\"false\" role=\"img\" width=\"1em\" height=\"1em\" preserveAspectRatio=\"xMidYMid meet\" viewBox=\"0 0 25 25\"><ellipse cx=\"12.5\" cy=\"5\" class=\"fill-[#4A4E57]\" rx=\"7.5\" ry=\"2\"></ellipse><path d=\"M12.5 15C16.6421 15 20 14.1046 20 13V20C20 21.1046 16.6421 22 12.5 22C8.35786 22 5 21.1046 5 20V13C5 14.1046 8.35786 15 12.5 15Z\" class=\"fill-[#86898F]\"></path><path d=\"M12.5 7C16.6421 7 20 6.10457 20 5V11.5C20 12.6046 16.6421 13.5 12.5 13.5C8.35786 13.5 5 12.6046 5 11.5V5C5 6.10457 8.35786 7 12.5 7Z\" class=\"fill-[#86898F]\"></path><path d=\"M5.23628 12C5.08204 12.1598 5 12.8273 5 13C5 14.1046 8.35786 15 12.5 15C16.6421 15 20 14.1046 20 13C20 12.8273 19.918 12.1598 19.7637 12C18.9311 12.8626 15.9947 13.5 12.5 13.5C9.0053 13.5 6.06886 12.8626 5.23628 12Z\" class=\"fill-white\"></path></svg></div></div>\n", "\t\t\t<div class=\"col-span-1 mt-0 px-4 pb-8 text-center sm:mt-12 2xl:pb-8\"><div class=\"mb-8 flex flex-none items-center justify-center gap-2\"><div class=\"bg-linear-to-l mr-2 h-px flex-1 from-gray-100 to-white dark:from-gray-800 dark:to-gray-950\"></div>\n", "\t\t\t\t\t<h2 class=\"text-balance text-base text-gray-500\">More than 50,000 organizations are using Hugging Face</h2>\n", "\t\t\t\t\t<div class=\"bg-linear-to-r ml-2 h-px flex-1 from-gray-100 to-white dark:from-gray-800 dark:to-gray-950\"></div></div>\n", "\t\t\t\t<div class=\"grid gap-3 text-left md:grid-cols-2 lg:grid-cols-4\"><article class=\"overview-card-wrapper   \"><a class=\"flex flex-1 items-center overflow-hidden p-2 \" href=\"/allenai\"><img alt=\"Ai<PERSON>'s profile picture\" class=\"mr-3 flex-none rounded-lg h-8 w-8\" src=\"https://cdn-avatars.huggingface.co/v1/production/uploads/652db071b62cf1f8463221e2/CxxwFiaomTa1MCX_B7-pT.png\">\n", "\t\t<div class=\"overflow-hidden pr-0.5 text-left leading-tight\"><div class=\"flex items-center gap-3\"><h4 href=\"/allenai\" class=\"truncate font-semibold \" title=\"Ai2\">Ai2</h4>\n", "\t\t\t\t<span class=\"bg-linear-to-br inline-block -skew-x-12 whitespace-nowrap border border-gray-200 from-gray-800 to-gray-900 font-semibold leading-none text-white will-change-transform dark:from-gray-50 dark:to-gray-100 dark:text-gray-900 rounded-md px-1.5 py-0.5 text-xs \"><span>Enterprise </span>\n", "\n", "\t\t</span></div>\n", "\t\t\t<div class=\"text-sm text-gray-400 truncate leading-tight\"><span class=\"capitalize\">non-profit</span>\n", "\t\t\t\t<span class=\"px-0.5 text-xs text-gray-300\">•</span>\n", "\t\t\t\t784 models\n", "\t\t\t\t<span class=\"px-0.5 text-xs text-gray-300\">•</span>\n", "\t\t\t\t3.95k followers\n", "\t\t\t\t</div></div></a>\n", "\t</article><article class=\"overview-card-wrapper   \"><a class=\"flex flex-1 items-center overflow-hidden p-2 \" href=\"/facebook\"><img alt=\"AI at Meta's profile picture\" class=\"mr-3 flex-none rounded-lg h-8 w-8\" src=\"https://cdn-avatars.huggingface.co/v1/production/uploads/1592839207516-noauth.png\">\n", "\t\t<div class=\"overflow-hidden pr-0.5 text-left leading-tight\"><div class=\"flex items-center gap-3\"><h4 href=\"/facebook\" class=\"truncate font-semibold \" title=\"AI at Meta\">AI at Meta</h4>\n", "\t\t\t\t<span class=\"bg-linear-to-br inline-block -skew-x-12 whitespace-nowrap border border-gray-200 from-gray-800 to-gray-900 font-semibold leading-none text-white will-change-transform dark:from-gray-50 dark:to-gray-100 dark:text-gray-900 rounded-md px-1.5 py-0.5 text-xs \"><span>Enterprise </span>\n", "\n", "\t\t</span></div>\n", "\t\t\t<div class=\"text-sm text-gray-400 truncate leading-tight\"><span class=\"capitalize\">company</span>\n", "\t\t\t\t<span class=\"px-0.5 text-xs text-gray-300\">•</span>\n", "\t\t\t\t2.22k models\n", "\t\t\t\t<span class=\"px-0.5 text-xs text-gray-300\">•</span>\n", "\t\t\t\t7.5k followers\n", "\t\t\t\t</div></div></a>\n", "\t</article><article class=\"overview-card-wrapper   \"><a class=\"flex flex-1 items-center overflow-hidden p-2 \" href=\"/amazon\"><img alt=\"Amazon's profile picture\" class=\"mr-3 flex-none rounded-lg h-8 w-8\" src=\"https://cdn-avatars.huggingface.co/v1/production/uploads/66f19ed428ae41c20c470792/8y7msN6A6W82LdQhQd85a.png\">\n", "\t\t<div class=\"overflow-hidden pr-0.5 text-left leading-tight\"><div class=\"flex items-center gap-3\"><h4 href=\"/amazon\" class=\"truncate font-semibold \" title=\"Amazon\">Amazon</h4>\n", "\t\t\t\t</div>\n", "\t\t\t<div class=\"text-sm text-gray-400 truncate leading-tight\"><span class=\"capitalize\">company</span>\n", "\t\t\t\t<span class=\"px-0.5 text-xs text-gray-300\">•</span>\n", "\t\t\t\t20 models\n", "\t\t\t\t<span class=\"px-0.5 text-xs text-gray-300\">•</span>\n", "\t\t\t\t3.39k followers\n", "\t\t\t\t</div></div></a>\n", "\t</article><article class=\"overview-card-wrapper   \"><a class=\"flex flex-1 items-center overflow-hidden p-2 \" href=\"/google\"><img alt=\"Google's profile picture\" class=\"mr-3 flex-none rounded-lg h-8 w-8\" src=\"https://cdn-avatars.huggingface.co/v1/production/uploads/5dd96eb166059660ed1ee413/WtA3YYitedOr9n02eHfJe.png\">\n", "\t\t<div class=\"overflow-hidden pr-0.5 text-left leading-tight\"><div class=\"flex items-center gap-3\"><h4 href=\"/google\" class=\"truncate font-semibold \" title=\"Google\">Google</h4>\n", "\t\t\t\t<span class=\"bg-linear-to-br inline-block -skew-x-12 whitespace-nowrap border border-gray-200 from-gray-800 to-gray-900 font-semibold leading-none text-white will-change-transform dark:from-gray-50 dark:to-gray-100 dark:text-gray-900 rounded-md px-1.5 py-0.5 text-xs \"><span>Enterprise </span>\n", "\n", "\t\t</span></div>\n", "\t\t\t<div class=\"text-sm text-gray-400 truncate leading-tight\"><span class=\"capitalize\">company</span>\n", "\t\t\t\t<span class=\"px-0.5 text-xs text-gray-300\">•</span>\n", "\t\t\t\t1.04k models\n", "\t\t\t\t<span class=\"px-0.5 text-xs text-gray-300\">•</span>\n", "\t\t\t\t26.8k followers\n", "\t\t\t\t</div></div></a>\n", "\t</article><article class=\"overview-card-wrapper   \"><a class=\"flex flex-1 items-center overflow-hidden p-2 \" href=\"/Intel\"><img alt=\"Intel's profile picture\" class=\"mr-3 flex-none rounded-lg h-8 w-8\" src=\"https://cdn-avatars.huggingface.co/v1/production/uploads/1616186257611-60104afcc75e19ac1738fe70.png\">\n", "\t\t<div class=\"overflow-hidden pr-0.5 text-left leading-tight\"><div class=\"flex items-center gap-3\"><h4 href=\"/Intel\" class=\"truncate font-semibold \" title=\"Intel\">Intel</h4>\n", "\t\t\t\t</div>\n", "\t\t\t<div class=\"text-sm text-gray-400 truncate leading-tight\"><span class=\"capitalize\">company</span>\n", "\t\t\t\t<span class=\"px-0.5 text-xs text-gray-300\">•</span>\n", "\t\t\t\t237 models\n", "\t\t\t\t<span class=\"px-0.5 text-xs text-gray-300\">•</span>\n", "\t\t\t\t2.95k followers\n", "\t\t\t\t</div></div></a>\n", "\t</article><article class=\"overview-card-wrapper   \"><a class=\"flex flex-1 items-center overflow-hidden p-2 \" href=\"/microsoft\"><img alt=\"Microsoft's profile picture\" class=\"mr-3 flex-none rounded-lg h-8 w-8\" src=\"https://cdn-avatars.huggingface.co/v1/production/uploads/1583646260758-5e64858c87403103f9f1055d.png\">\n", "\t\t<div class=\"overflow-hidden pr-0.5 text-left leading-tight\"><div class=\"flex items-center gap-3\"><h4 href=\"/microsoft\" class=\"truncate font-semibold \" title=\"Microsoft\">Microsoft</h4>\n", "\t\t\t\t</div>\n", "\t\t\t<div class=\"text-sm text-gray-400 truncate leading-tight\"><span class=\"capitalize\">company</span>\n", "\t\t\t\t<span class=\"px-0.5 text-xs text-gray-300\">•</span>\n", "\t\t\t\t424 models\n", "\t\t\t\t<span class=\"px-0.5 text-xs text-gray-300\">•</span>\n", "\t\t\t\t14.7k followers\n", "\t\t\t\t</div></div></a>\n", "\t</article><article class=\"overview-card-wrapper   \"><a class=\"flex flex-1 items-center overflow-hidden p-2 \" href=\"/grammarly\"><img alt=\"Grammar<PERSON>'s profile picture\" class=\"mr-3 flex-none rounded-lg h-8 w-8\" src=\"https://cdn-avatars.huggingface.co/v1/production/uploads/60985a0547dc3dbf8a976607/rRv-TjtvhN66uwh-xYaCf.png\">\n", "\t\t<div class=\"overflow-hidden pr-0.5 text-left leading-tight\"><div class=\"flex items-center gap-3\"><h4 href=\"/grammarly\" class=\"truncate font-semibold \" title=\"Grammarly\">Grammarly</h4>\n", "\t\t\t\t<span class=\"bg-linear-to-br inline-block -skew-x-12 whitespace-nowrap border border-gray-200 from-gray-800 to-gray-900 font-semibold leading-none text-white will-change-transform dark:from-gray-50 dark:to-gray-100 dark:text-gray-900 rounded-md px-1.5 py-0.5 text-xs \"><span>Team </span></span></div>\n", "\t\t\t<div class=\"text-sm text-gray-400 truncate leading-tight\"><span class=\"capitalize\">company</span>\n", "\t\t\t\t<span class=\"px-0.5 text-xs text-gray-300\">•</span>\n", "\t\t\t\t11 models\n", "\t\t\t\t<span class=\"px-0.5 text-xs text-gray-300\">•</span>\n", "\t\t\t\t175 followers\n", "\t\t\t\t</div></div></a>\n", "\t</article><article class=\"overview-card-wrapper   \"><a class=\"flex flex-1 items-center overflow-hidden p-2 \" href=\"/Writer\"><img alt=\"Writer's profile picture\" class=\"mr-3 flex-none rounded-lg h-8 w-8\" src=\"https://cdn-avatars.huggingface.co/v1/production/uploads/1625001569797-60db8b5ad8b4797b129145d5.png\">\n", "\t\t<div class=\"overflow-hidden pr-0.5 text-left leading-tight\"><div class=\"flex items-center gap-3\"><h4 href=\"/Writer\" class=\"truncate font-semibold \" title=\"Writer\">Writer</h4>\n", "\t\t\t\t<span class=\"bg-linear-to-br inline-block -skew-x-12 whitespace-nowrap border border-gray-200 from-gray-800 to-gray-900 font-semibold leading-none text-white will-change-transform dark:from-gray-50 dark:to-gray-100 dark:text-gray-900 rounded-md px-1.5 py-0.5 text-xs \"><span>Enterprise </span>\n", "\n", "\t\t</span></div>\n", "\t\t\t<div class=\"text-sm text-gray-400 truncate leading-tight\"><span class=\"capitalize\">company</span>\n", "\t\t\t\t<span class=\"px-0.5 text-xs text-gray-300\">•</span>\n", "\t\t\t\t21 models\n", "\t\t\t\t<span class=\"px-0.5 text-xs text-gray-300\">•</span>\n", "\t\t\t\t328 followers\n", "\t\t\t\t</div></div></a>\n", "\t</article></div></div></div>\n", "\t\t<div class=\"bg-linear-to-t mb-8 flex h-40 w-full flex-col items-stretch rounded-b-[50%] from-gray-100/60 via-white to-white pt-16 text-center dark:from-gray-900/60 dark:via-gray-950 dark:to-gray-950\"></div>\n", "\n", "\t\t<div class=\"flex h-56 w-full flex-col items-stretch pt-12 text-center\"><svg class=\"text-3xl 2xl:text-4xl self-center text-gray-900 dark:text-gray-500 mb-2 sm:mb-4 flex-none\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" aria-hidden=\"true\" focusable=\"false\" role=\"img\" width=\"1em\" height=\"1em\" preserveAspectRatio=\"xMidYMid meet\" viewBox=\"0 0 16 17\"><path fill=\"currentColor\" fill-opacity=\".25\" fill-rule=\"evenodd\" d=\"M10.4 3c-1 0-1.9.8-1.9 1.8v1.8c0 1 .8 1.9 1.9 1.9h1.8c1 0 1.9-.8 1.9-1.9V4.8a2 2 0 0 0-1.9-1.9h-1.8ZM3.9 8.4c-1 0-1.9.8-1.9 1.9v1.8c0 1 .8 1.9 1.9 1.9h1.8a2 2 0 0 0 1.9-1.9v-1.8a2 2 0 0 0-1.9-1.9H4Z\" clip-rule=\"evenodd\"></path><path fill=\"currentColor\" fill-opacity=\".5\" fill-rule=\"evenodd\" d=\"M3.9 2C2.9 2 2 2.8 2 3.9v1.8c0 1 .8 1.9 1.9 1.9h1.8a2 2 0 0 0 1.9-1.9V4a2 2 0 0 0-1.9-1.9H4Zm6.5 7.4a2 2 0 0 0-1.9 1.9V13c0 1 .8 1.9 1.9 1.9h1.8c1 0 1.9-.8 1.9-1.9v-1.8a2 2 0 0 0-1.9-1.9h-1.8Z\" clip-rule=\"evenodd\"></path></svg>\n", "\n", "\t\t\t<div class=\"mb-2 flex items-center justify-center gap-2 text-xl font-bold\"><div class=\"bg-linear-to-l mr-4 h-px flex-1 from-gray-100 to-white dark:from-gray-800 dark:to-gray-950\"></div>\n", "\t\t\t\t<h2 class=\"text-3xl font-bold 2xl:text-4xl\">Our Open Source</h2>\n", "\t\t\t\t<div class=\"bg-linear-to-r ml-4 h-px flex-1 from-gray-100 to-white dark:from-gray-800 dark:to-gray-950\"></div></div>\n", "\n", "\t\t\t<p class=\"text-balance text-lg text-gray-500 2xl:text-xl\">We are building the foundation of ML tooling with the community.\n", "\t\t\t</p></div>\n", "\t\t<div class=\"relative mx-auto grid max-w-6xl translate-y-14 grid-cols-2 gap-3 overflow-hidden sm:gap-5 lg:grid-cols-4 2xl:max-w-7xl\"><div class=\"bg-linear-to-b absolute inset-x-0 h-1/2 translate-y-1/2 rotate-6 select-none rounded-[50%] from-pink-500/10 via-white to-indigo-500/10 blur-2xl dark:via-gray-950\"></div>\n", "\t\t\t\n", "\t\t\t\t\n", "\t\t\t\t<a href=\"/docs/transformers\" class=\"relative col-span-1 flex h-48 flex-col rounded-[1.6rem] border border-gray-100 bg-white/80 p-5 shadow-sm transition-transform will-change-transform hover:translate-y-px hover:shadow-inner dark:border-gray-800 dark:bg-gray-900/60 sm:h-56 sm:p-6 2xl:h-72 2xl:p-10  \"><h4 class=\"leading-tight! flex items-center gap-1.5 text-lg font-bold sm:gap-2 sm:text-xl 2xl:text-2xl\"><div class=\"h-2 w-2 flex-none rounded-full bg-orange-500\"></div>\n", "\t\t\t\t\t\t\tTransformers</h4>\n", "\t\t\t\t\t\t<div class=\"flex items-center gap-1.5 text-lg\"><svg class=\"text-xs text-gray-700\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" aria-hidden=\"true\" focusable=\"false\" role=\"img\" width=\"1.03em\" height=\"1em\" preserveAspectRatio=\"xMidYMid meet\" viewBox=\"0 0 256 250\"><path d=\"M128.001 0C57.317 0 0 57.307 0 128.001c0 56.554 36.676 104.535 87.535 121.46c6.397 1.185 8.746-2.777 8.746-6.158c0-3.052-.12-13.135-.174-23.83c-35.61 7.742-43.124-15.103-43.124-15.103c-5.823-14.795-14.213-18.73-14.213-18.73c-11.613-7.944.876-7.78.876-7.78c12.853.902 19.621 13.19 19.621 13.19c11.417 19.568 29.945 13.911 37.249 10.64c1.149-8.272 4.466-13.92 8.127-17.116c-28.431-3.236-58.318-14.212-58.318-63.258c0-13.975 5-25.394 13.188-34.358c-1.329-3.224-5.71-16.242 1.24-33.874c0 0 10.749-3.44 35.21 13.121c10.21-2.836 21.16-4.258 32.038-4.307c10.878.049 21.837 1.47 32.066 4.307c24.431-16.56 35.165-13.12 35.165-13.12c6.967 17.63 2.584 30.65 1.255 33.873c8.207 8.964 13.173 20.383 13.173 34.358c0 49.163-29.944 59.988-58.447 63.157c4.591 3.972 8.682 11.762 8.682 23.704c0 17.126-.148 30.91-.148 35.126c0 3.407 2.304 7.398 8.792 6.14C219.37 232.5 256 184.537 256 128.002C256 57.307 198.691 0 128.001 0zm-80.06 182.34c-.282.636-1.283.827-2.194.39c-.929-.417-1.45-1.284-1.15-1.922c.276-.655 1.279-.838 2.205-.399c.93.418 1.46 1.293 1.139 1.931zm6.296 5.618c-.61.566-1.804.303-2.614-.591c-.837-.892-.994-2.086-.375-2.66c.63-.566 1.787-.301 2.626.591c.838.903 1 2.088.363 2.66zm4.32 7.188c-.785.545-2.067.034-2.86-1.104c-.784-1.138-.784-2.503.017-3.05c.795-.547 2.058-.055 2.861 1.075c.782 1.157.782 2.522-.019 3.08zm7.304 8.325c-.701.774-2.196.566-3.29-.49c-1.119-1.032-1.43-2.496-.726-3.27c.71-.776 2.213-.558 3.315.49c1.11 1.03 1.45 2.505.701 3.27zm9.442 2.81c-.31 1.003-1.75 1.459-3.199 1.033c-1.448-.439-2.395-1.613-2.103-2.626c.301-1.01 1.747-1.484 3.207-1.028c1.446.436 2.396 1.602 2.095 2.622zm10.744 1.193c.036 1.055-1.193 1.93-2.715 1.95c-1.53.034-2.769-.82-2.786-1.86c0-1.065 1.202-1.932 2.733-1.958c1.522-.03 2.768.818 2.768 1.868zm10.555-.405c.182 1.03-.875 2.088-2.387 2.37c-1.485.271-2.861-.365-3.05-1.386c-.184-1.056.893-2.114 2.376-2.387c1.514-.263 2.868.356 3.061 1.403z\" fill=\"currentColor\"></path></svg>\n", "\t\t\t\t\t\t\t\t<span>148,959</span></div>\n", "\t\t\t\t\t\t<p class=\"mt-auto text-sm text-gray-500 md:text-base 2xl:text-lg\">State-of-the-art AI models for PyTorch</p>\n", "\t\t\t\t\t</a>\n", "\t\t\t\t\n", "\t\t\t\t<a href=\"/docs/diffusers\" class=\"relative col-span-1 flex h-48 flex-col rounded-[1.6rem] border border-gray-100 bg-white/80 p-5 shadow-sm transition-transform will-change-transform hover:translate-y-px hover:shadow-inner dark:border-gray-800 dark:bg-gray-900/60 sm:h-56 sm:p-6 2xl:h-72 2xl:p-10 lg:hover:translate-y-[calc(3rem+1px)]! lg:translate-y-12 \"><h4 class=\"leading-tight! flex items-center gap-1.5 text-lg font-bold sm:gap-2 sm:text-xl 2xl:text-2xl\"><div class=\"h-2 w-2 flex-none rounded-full bg-violet-500\"></div>\n", "\t\t\t\t\t\t\tDiffusers</h4>\n", "\t\t\t\t\t\t<div class=\"flex items-center gap-1.5 text-lg\"><svg class=\"text-xs text-gray-700\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" aria-hidden=\"true\" focusable=\"false\" role=\"img\" width=\"1.03em\" height=\"1em\" preserveAspectRatio=\"xMidYMid meet\" viewBox=\"0 0 256 250\"><path d=\"M128.001 0C57.317 0 0 57.307 0 128.001c0 56.554 36.676 104.535 87.535 121.46c6.397 1.185 8.746-2.777 8.746-6.158c0-3.052-.12-13.135-.174-23.83c-35.61 7.742-43.124-15.103-43.124-15.103c-5.823-14.795-14.213-18.73-14.213-18.73c-11.613-7.944.876-7.78.876-7.78c12.853.902 19.621 13.19 19.621 13.19c11.417 19.568 29.945 13.911 37.249 10.64c1.149-8.272 4.466-13.92 8.127-17.116c-28.431-3.236-58.318-14.212-58.318-63.258c0-13.975 5-25.394 13.188-34.358c-1.329-3.224-5.71-16.242 1.24-33.874c0 0 10.749-3.44 35.21 13.121c10.21-2.836 21.16-4.258 32.038-4.307c10.878.049 21.837 1.47 32.066 4.307c24.431-16.56 35.165-13.12 35.165-13.12c6.967 17.63 2.584 30.65 1.255 33.873c8.207 8.964 13.173 20.383 13.173 34.358c0 49.163-29.944 59.988-58.447 63.157c4.591 3.972 8.682 11.762 8.682 23.704c0 17.126-.148 30.91-.148 35.126c0 3.407 2.304 7.398 8.792 6.14C219.37 232.5 256 184.537 256 128.002C256 57.307 198.691 0 128.001 0zm-80.06 182.34c-.282.636-1.283.827-2.194.39c-.929-.417-1.45-1.284-1.15-1.922c.276-.655 1.279-.838 2.205-.399c.93.418 1.46 1.293 1.139 1.931zm6.296 5.618c-.61.566-1.804.303-2.614-.591c-.837-.892-.994-2.086-.375-2.66c.63-.566 1.787-.301 2.626.591c.838.903 1 2.088.363 2.66zm4.32 7.188c-.785.545-2.067.034-2.86-1.104c-.784-1.138-.784-2.503.017-3.05c.795-.547 2.058-.055 2.861 1.075c.782 1.157.782 2.522-.019 3.08zm7.304 8.325c-.701.774-2.196.566-3.29-.49c-1.119-1.032-1.43-2.496-.726-3.27c.71-.776 2.213-.558 3.315.49c1.11 1.03 1.45 2.505.701 3.27zm9.442 2.81c-.31 1.003-1.75 1.459-3.199 1.033c-1.448-.439-2.395-1.613-2.103-2.626c.301-1.01 1.747-1.484 3.207-1.028c1.446.436 2.396 1.602 2.095 2.622zm10.744 1.193c.036 1.055-1.193 1.93-2.715 1.95c-1.53.034-2.769-.82-2.786-1.86c0-1.065 1.202-1.932 2.733-1.958c1.522-.03 2.768.818 2.768 1.868zm10.555-.405c.182 1.03-.875 2.088-2.387 2.37c-1.485.271-2.861-.365-3.05-1.386c-.184-1.056.893-2.114 2.376-2.387c1.514-.263 2.868.356 3.061 1.403z\" fill=\"currentColor\"></path></svg>\n", "\t\t\t\t\t\t\t\t<span>30,523</span></div>\n", "\t\t\t\t\t\t<p class=\"mt-auto text-sm text-gray-500 md:text-base 2xl:text-lg\">State-of-the-art Diffusion models in PyTorch</p>\n", "\t\t\t\t\t</a>\n", "\t\t\t\t\n", "\t\t\t\t<a href=\"/docs/safetensors\" class=\"relative col-span-1 flex h-48 flex-col rounded-[1.6rem] border border-gray-100 bg-white/80 p-5 shadow-sm transition-transform will-change-transform hover:translate-y-px hover:shadow-inner dark:border-gray-800 dark:bg-gray-900/60 sm:h-56 sm:p-6 2xl:h-72 2xl:p-10  \"><h4 class=\"leading-tight! flex items-center gap-1.5 text-lg font-bold sm:gap-2 sm:text-xl 2xl:text-2xl\"><div class=\"h-2 w-2 flex-none rounded-full bg-violet-500\"></div>\n", "\t\t\t\t\t\t\tSafetensors</h4>\n", "\t\t\t\t\t\t<div class=\"flex items-center gap-1.5 text-lg\"><svg class=\"text-xs text-gray-700\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" aria-hidden=\"true\" focusable=\"false\" role=\"img\" width=\"1.03em\" height=\"1em\" preserveAspectRatio=\"xMidYMid meet\" viewBox=\"0 0 256 250\"><path d=\"M128.001 0C57.317 0 0 57.307 0 128.001c0 56.554 36.676 104.535 87.535 121.46c6.397 1.185 8.746-2.777 8.746-6.158c0-3.052-.12-13.135-.174-23.83c-35.61 7.742-43.124-15.103-43.124-15.103c-5.823-14.795-14.213-18.73-14.213-18.73c-11.613-7.944.876-7.78.876-7.78c12.853.902 19.621 13.19 19.621 13.19c11.417 19.568 29.945 13.911 37.249 10.64c1.149-8.272 4.466-13.92 8.127-17.116c-28.431-3.236-58.318-14.212-58.318-63.258c0-13.975 5-25.394 13.188-34.358c-1.329-3.224-5.71-16.242 1.24-33.874c0 0 10.749-3.44 35.21 13.121c10.21-2.836 21.16-4.258 32.038-4.307c10.878.049 21.837 1.47 32.066 4.307c24.431-16.56 35.165-13.12 35.165-13.12c6.967 17.63 2.584 30.65 1.255 33.873c8.207 8.964 13.173 20.383 13.173 34.358c0 49.163-29.944 59.988-58.447 63.157c4.591 3.972 8.682 11.762 8.682 23.704c0 17.126-.148 30.91-.148 35.126c0 3.407 2.304 7.398 8.792 6.14C219.37 232.5 256 184.537 256 128.002C256 57.307 198.691 0 128.001 0zm-80.06 182.34c-.282.636-1.283.827-2.194.39c-.929-.417-1.45-1.284-1.15-1.922c.276-.655 1.279-.838 2.205-.399c.93.418 1.46 1.293 1.139 1.931zm6.296 5.618c-.61.566-1.804.303-2.614-.591c-.837-.892-.994-2.086-.375-2.66c.63-.566 1.787-.301 2.626.591c.838.903 1 2.088.363 2.66zm4.32 7.188c-.785.545-2.067.034-2.86-1.104c-.784-1.138-.784-2.503.017-3.05c.795-.547 2.058-.055 2.861 1.075c.782 1.157.782 2.522-.019 3.08zm7.304 8.325c-.701.774-2.196.566-3.29-.49c-1.119-1.032-1.43-2.496-.726-3.27c.71-.776 2.213-.558 3.315.49c1.11 1.03 1.45 2.505.701 3.27zm9.442 2.81c-.31 1.003-1.75 1.459-3.199 1.033c-1.448-.439-2.395-1.613-2.103-2.626c.301-1.01 1.747-1.484 3.207-1.028c1.446.436 2.396 1.602 2.095 2.622zm10.744 1.193c.036 1.055-1.193 1.93-2.715 1.95c-1.53.034-2.769-.82-2.786-1.86c0-1.065 1.202-1.932 2.733-1.958c1.522-.03 2.768.818 2.768 1.868zm10.555-.405c.182 1.03-.875 2.088-2.387 2.37c-1.485.271-2.861-.365-3.05-1.386c-.184-1.056.893-2.114 2.376-2.387c1.514-.263 2.868.356 3.061 1.403z\" fill=\"currentColor\"></path></svg>\n", "\t\t\t\t\t\t\t\t<span>3,425</span></div>\n", "\t\t\t\t\t\t<p class=\"mt-auto text-sm text-gray-500 md:text-base 2xl:text-lg\">Safe way to store/distribute neural network weights</p>\n", "\t\t\t\t\t</a>\n", "\t\t\t\t\n", "\t\t\t\t<a href=\"/docs/huggingface_hub\" class=\"relative col-span-1 flex h-48 flex-col rounded-[1.6rem] border border-gray-100 bg-white/80 p-5 shadow-sm transition-transform will-change-transform hover:translate-y-px hover:shadow-inner dark:border-gray-800 dark:bg-gray-900/60 sm:h-56 sm:p-6 2xl:h-72 2xl:p-10 lg:hover:translate-y-[calc(3rem+1px)]! lg:translate-y-12 \"><h4 class=\"leading-tight! flex items-center gap-1.5 text-lg font-bold sm:gap-2 sm:text-xl 2xl:text-2xl\"><div class=\"h-2 w-2 flex-none rounded-full bg-indigo-500\"></div>\n", "\t\t\t\t\t\t\tHub Python Library</h4>\n", "\t\t\t\t\t\t<div class=\"flex items-center gap-1.5 text-lg\"><svg class=\"text-xs text-gray-700\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" aria-hidden=\"true\" focusable=\"false\" role=\"img\" width=\"1.03em\" height=\"1em\" preserveAspectRatio=\"xMidYMid meet\" viewBox=\"0 0 256 250\"><path d=\"M128.001 0C57.317 0 0 57.307 0 128.001c0 56.554 36.676 104.535 87.535 121.46c6.397 1.185 8.746-2.777 8.746-6.158c0-3.052-.12-13.135-.174-23.83c-35.61 7.742-43.124-15.103-43.124-15.103c-5.823-14.795-14.213-18.73-14.213-18.73c-11.613-7.944.876-7.78.876-7.78c12.853.902 19.621 13.19 19.621 13.19c11.417 19.568 29.945 13.911 37.249 10.64c1.149-8.272 4.466-13.92 8.127-17.116c-28.431-3.236-58.318-14.212-58.318-63.258c0-13.975 5-25.394 13.188-34.358c-1.329-3.224-5.71-16.242 1.24-33.874c0 0 10.749-3.44 35.21 13.121c10.21-2.836 21.16-4.258 32.038-4.307c10.878.049 21.837 1.47 32.066 4.307c24.431-16.56 35.165-13.12 35.165-13.12c6.967 17.63 2.584 30.65 1.255 33.873c8.207 8.964 13.173 20.383 13.173 34.358c0 49.163-29.944 59.988-58.447 63.157c4.591 3.972 8.682 11.762 8.682 23.704c0 17.126-.148 30.91-.148 35.126c0 3.407 2.304 7.398 8.792 6.14C219.37 232.5 256 184.537 256 128.002C256 57.307 198.691 0 128.001 0zm-80.06 182.34c-.282.636-1.283.827-2.194.39c-.929-.417-1.45-1.284-1.15-1.922c.276-.655 1.279-.838 2.205-.399c.93.418 1.46 1.293 1.139 1.931zm6.296 5.618c-.61.566-1.804.303-2.614-.591c-.837-.892-.994-2.086-.375-2.66c.63-.566 1.787-.301 2.626.591c.838.903 1 2.088.363 2.66zm4.32 7.188c-.785.545-2.067.034-2.86-1.104c-.784-1.138-.784-2.503.017-3.05c.795-.547 2.058-.055 2.861 1.075c.782 1.157.782 2.522-.019 3.08zm7.304 8.325c-.701.774-2.196.566-3.29-.49c-1.119-1.032-1.43-2.496-.726-3.27c.71-.776 2.213-.558 3.315.49c1.11 1.03 1.45 2.505.701 3.27zm9.442 2.81c-.31 1.003-1.75 1.459-3.199 1.033c-1.448-.439-2.395-1.613-2.103-2.626c.301-1.01 1.747-1.484 3.207-1.028c1.446.436 2.396 1.602 2.095 2.622zm10.744 1.193c.036 1.055-1.193 1.93-2.715 1.95c-1.53.034-2.769-.82-2.786-1.86c0-1.065 1.202-1.932 2.733-1.958c1.522-.03 2.768.818 2.768 1.868zm10.555-.405c.182 1.03-.875 2.088-2.387 2.37c-1.485.271-2.861-.365-3.05-1.386c-.184-1.056.893-2.114 2.376-2.387c1.514-.263 2.868.356 3.061 1.403z\" fill=\"currentColor\"></path></svg>\n", "\t\t\t\t\t\t\t\t<span>2,877</span></div>\n", "\t\t\t\t\t\t<p class=\"mt-auto text-sm text-gray-500 md:text-base 2xl:text-lg\">Python client to interact with the Hugging Face Hub</p>\n", "\t\t\t\t\t</a>\n", "\t\t\t\t\n", "\t\t\t\t<a href=\"/docs/tokenizers\" class=\"relative col-span-1 flex h-48 flex-col rounded-[1.6rem] border border-gray-100 bg-white/80 p-5 shadow-sm transition-transform will-change-transform hover:translate-y-px hover:shadow-inner dark:border-gray-800 dark:bg-gray-900/60 sm:h-56 sm:p-6 2xl:h-72 2xl:p-10  \"><h4 class=\"leading-tight! flex items-center gap-1.5 text-lg font-bold sm:gap-2 sm:text-xl 2xl:text-2xl\"><div class=\"h-2 w-2 flex-none rounded-full bg-lime-500\"></div>\n", "\t\t\t\t\t\t\tTokenizers</h4>\n", "\t\t\t\t\t\t<div class=\"flex items-center gap-1.5 text-lg\"><svg class=\"text-xs text-gray-700\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" aria-hidden=\"true\" focusable=\"false\" role=\"img\" width=\"1.03em\" height=\"1em\" preserveAspectRatio=\"xMidYMid meet\" viewBox=\"0 0 256 250\"><path d=\"M128.001 0C57.317 0 0 57.307 0 128.001c0 56.554 36.676 104.535 87.535 121.46c6.397 1.185 8.746-2.777 8.746-6.158c0-3.052-.12-13.135-.174-23.83c-35.61 7.742-43.124-15.103-43.124-15.103c-5.823-14.795-14.213-18.73-14.213-18.73c-11.613-7.944.876-7.78.876-7.78c12.853.902 19.621 13.19 19.621 13.19c11.417 19.568 29.945 13.911 37.249 10.64c1.149-8.272 4.466-13.92 8.127-17.116c-28.431-3.236-58.318-14.212-58.318-63.258c0-13.975 5-25.394 13.188-34.358c-1.329-3.224-5.71-16.242 1.24-33.874c0 0 10.749-3.44 35.21 13.121c10.21-2.836 21.16-4.258 32.038-4.307c10.878.049 21.837 1.47 32.066 4.307c24.431-16.56 35.165-13.12 35.165-13.12c6.967 17.63 2.584 30.65 1.255 33.873c8.207 8.964 13.173 20.383 13.173 34.358c0 49.163-29.944 59.988-58.447 63.157c4.591 3.972 8.682 11.762 8.682 23.704c0 17.126-.148 30.91-.148 35.126c0 3.407 2.304 7.398 8.792 6.14C219.37 232.5 256 184.537 256 128.002C256 57.307 198.691 0 128.001 0zm-80.06 182.34c-.282.636-1.283.827-2.194.39c-.929-.417-1.45-1.284-1.15-1.922c.276-.655 1.279-.838 2.205-.399c.93.418 1.46 1.293 1.139 1.931zm6.296 5.618c-.61.566-1.804.303-2.614-.591c-.837-.892-.994-2.086-.375-2.66c.63-.566 1.787-.301 2.626.591c.838.903 1 2.088.363 2.66zm4.32 7.188c-.785.545-2.067.034-2.86-1.104c-.784-1.138-.784-2.503.017-3.05c.795-.547 2.058-.055 2.861 1.075c.782 1.157.782 2.522-.019 3.08zm7.304 8.325c-.701.774-2.196.566-3.29-.49c-1.119-1.032-1.43-2.496-.726-3.27c.71-.776 2.213-.558 3.315.49c1.11 1.03 1.45 2.505.701 3.27zm9.442 2.81c-.31 1.003-1.75 1.459-3.199 1.033c-1.448-.439-2.395-1.613-2.103-2.626c.301-1.01 1.747-1.484 3.207-1.028c1.446.436 2.396 1.602 2.095 2.622zm10.744 1.193c.036 1.055-1.193 1.93-2.715 1.95c-1.53.034-2.769-.82-2.786-1.86c0-1.065 1.202-1.932 2.733-1.958c1.522-.03 2.768.818 2.768 1.868zm10.555-.405c.182 1.03-.875 2.088-2.387 2.37c-1.485.271-2.861-.365-3.05-1.386c-.184-1.056.893-2.114 2.376-2.387c1.514-.263 2.868.356 3.061 1.403z\" fill=\"currentColor\"></path></svg>\n", "\t\t\t\t\t\t\t\t<span>10,033</span></div>\n", "\t\t\t\t\t\t<p class=\"mt-auto text-sm text-gray-500 md:text-base 2xl:text-lg\">Fast tokenizers optimized for research &amp; production</p>\n", "\t\t\t\t\t</a>\n", "\t\t\t\t\n", "\t\t\t\t<a href=\"/docs/trl\" class=\"relative col-span-1 flex h-48 flex-col rounded-[1.6rem] border border-gray-100 bg-white/80 p-5 shadow-sm transition-transform will-change-transform hover:translate-y-px hover:shadow-inner dark:border-gray-800 dark:bg-gray-900/60 sm:h-56 sm:p-6 2xl:h-72 2xl:p-10 lg:hover:translate-y-[calc(3rem+1px)]! lg:translate-y-12 \"><h4 class=\"leading-tight! flex items-center gap-1.5 text-lg font-bold sm:gap-2 sm:text-xl 2xl:text-2xl\"><div class=\"h-2 w-2 flex-none rounded-full bg-blue-500\"></div>\n", "\t\t\t\t\t\t\tTRL</h4>\n", "\t\t\t\t\t\t<div class=\"flex items-center gap-1.5 text-lg\"><svg class=\"text-xs text-gray-700\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" aria-hidden=\"true\" focusable=\"false\" role=\"img\" width=\"1.03em\" height=\"1em\" preserveAspectRatio=\"xMidYMid meet\" viewBox=\"0 0 256 250\"><path d=\"M128.001 0C57.317 0 0 57.307 0 128.001c0 56.554 36.676 104.535 87.535 121.46c6.397 1.185 8.746-2.777 8.746-6.158c0-3.052-.12-13.135-.174-23.83c-35.61 7.742-43.124-15.103-43.124-15.103c-5.823-14.795-14.213-18.73-14.213-18.73c-11.613-7.944.876-7.78.876-7.78c12.853.902 19.621 13.19 19.621 13.19c11.417 19.568 29.945 13.911 37.249 10.64c1.149-8.272 4.466-13.92 8.127-17.116c-28.431-3.236-58.318-14.212-58.318-63.258c0-13.975 5-25.394 13.188-34.358c-1.329-3.224-5.71-16.242 1.24-33.874c0 0 10.749-3.44 35.21 13.121c10.21-2.836 21.16-4.258 32.038-4.307c10.878.049 21.837 1.47 32.066 4.307c24.431-16.56 35.165-13.12 35.165-13.12c6.967 17.63 2.584 30.65 1.255 33.873c8.207 8.964 13.173 20.383 13.173 34.358c0 49.163-29.944 59.988-58.447 63.157c4.591 3.972 8.682 11.762 8.682 23.704c0 17.126-.148 30.91-.148 35.126c0 3.407 2.304 7.398 8.792 6.14C219.37 232.5 256 184.537 256 128.002C256 57.307 198.691 0 128.001 0zm-80.06 182.34c-.282.636-1.283.827-2.194.39c-.929-.417-1.45-1.284-1.15-1.922c.276-.655 1.279-.838 2.205-.399c.93.418 1.46 1.293 1.139 1.931zm6.296 5.618c-.61.566-1.804.303-2.614-.591c-.837-.892-.994-2.086-.375-2.66c.63-.566 1.787-.301 2.626.591c.838.903 1 2.088.363 2.66zm4.32 7.188c-.785.545-2.067.034-2.86-1.104c-.784-1.138-.784-2.503.017-3.05c.795-.547 2.058-.055 2.861 1.075c.782 1.157.782 2.522-.019 3.08zm7.304 8.325c-.701.774-2.196.566-3.29-.49c-1.119-1.032-1.43-2.496-.726-3.27c.71-.776 2.213-.558 3.315.49c1.11 1.03 1.45 2.505.701 3.27zm9.442 2.81c-.31 1.003-1.75 1.459-3.199 1.033c-1.448-.439-2.395-1.613-2.103-2.626c.301-1.01 1.747-1.484 3.207-1.028c1.446.436 2.396 1.602 2.095 2.622zm10.744 1.193c.036 1.055-1.193 1.93-2.715 1.95c-1.53.034-2.769-.82-2.786-1.86c0-1.065 1.202-1.932 2.733-1.958c1.522-.03 2.768.818 2.768 1.868zm10.555-.405c.182 1.03-.875 2.088-2.387 2.37c-1.485.271-2.861-.365-3.05-1.386c-.184-1.056.893-2.114 2.376-2.387c1.514-.263 2.868.356 3.061 1.403z\" fill=\"currentColor\"></path></svg>\n", "\t\t\t\t\t\t\t\t<span>15,297</span></div>\n", "\t\t\t\t\t\t<p class=\"mt-auto text-sm text-gray-500 md:text-base 2xl:text-lg\">Train transformers LMs with reinforcement learning</p>\n", "\t\t\t\t\t</a>\n", "\t\t\t\t\n", "\t\t\t\t<a href=\"/docs/transformers.js\" class=\"relative col-span-1 flex h-48 flex-col rounded-[1.6rem] border border-gray-100 bg-white/80 p-5 shadow-sm transition-transform will-change-transform hover:translate-y-px hover:shadow-inner dark:border-gray-800 dark:bg-gray-900/60 sm:h-56 sm:p-6 2xl:h-72 2xl:p-10  \"><h4 class=\"leading-tight! flex items-center gap-1.5 text-lg font-bold sm:gap-2 sm:text-xl 2xl:text-2xl\"><div class=\"h-2 w-2 flex-none rounded-full bg-orange-500\"></div>\n", "\t\t\t\t\t\t\tTransformers.js</h4>\n", "\t\t\t\t\t\t<div class=\"flex items-center gap-1.5 text-lg\"><svg class=\"text-xs text-gray-700\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" aria-hidden=\"true\" focusable=\"false\" role=\"img\" width=\"1.03em\" height=\"1em\" preserveAspectRatio=\"xMidYMid meet\" viewBox=\"0 0 256 250\"><path d=\"M128.001 0C57.317 0 0 57.307 0 128.001c0 56.554 36.676 104.535 87.535 121.46c6.397 1.185 8.746-2.777 8.746-6.158c0-3.052-.12-13.135-.174-23.83c-35.61 7.742-43.124-15.103-43.124-15.103c-5.823-14.795-14.213-18.73-14.213-18.73c-11.613-7.944.876-7.78.876-7.78c12.853.902 19.621 13.19 19.621 13.19c11.417 19.568 29.945 13.911 37.249 10.64c1.149-8.272 4.466-13.92 8.127-17.116c-28.431-3.236-58.318-14.212-58.318-63.258c0-13.975 5-25.394 13.188-34.358c-1.329-3.224-5.71-16.242 1.24-33.874c0 0 10.749-3.44 35.21 13.121c10.21-2.836 21.16-4.258 32.038-4.307c10.878.049 21.837 1.47 32.066 4.307c24.431-16.56 35.165-13.12 35.165-13.12c6.967 17.63 2.584 30.65 1.255 33.873c8.207 8.964 13.173 20.383 13.173 34.358c0 49.163-29.944 59.988-58.447 63.157c4.591 3.972 8.682 11.762 8.682 23.704c0 17.126-.148 30.91-.148 35.126c0 3.407 2.304 7.398 8.792 6.14C219.37 232.5 256 184.537 256 128.002C256 57.307 198.691 0 128.001 0zm-80.06 182.34c-.282.636-1.283.827-2.194.39c-.929-.417-1.45-1.284-1.15-1.922c.276-.655 1.279-.838 2.205-.399c.93.418 1.46 1.293 1.139 1.931zm6.296 5.618c-.61.566-1.804.303-2.614-.591c-.837-.892-.994-2.086-.375-2.66c.63-.566 1.787-.301 2.626.591c.838.903 1 2.088.363 2.66zm4.32 7.188c-.785.545-2.067.034-2.86-1.104c-.784-1.138-.784-2.503.017-3.05c.795-.547 2.058-.055 2.861 1.075c.782 1.157.782 2.522-.019 3.08zm7.304 8.325c-.701.774-2.196.566-3.29-.49c-1.119-1.032-1.43-2.496-.726-3.27c.71-.776 2.213-.558 3.315.49c1.11 1.03 1.45 2.505.701 3.27zm9.442 2.81c-.31 1.003-1.75 1.459-3.199 1.033c-1.448-.439-2.395-1.613-2.103-2.626c.301-1.01 1.747-1.484 3.207-1.028c1.446.436 2.396 1.602 2.095 2.622zm10.744 1.193c.036 1.055-1.193 1.93-2.715 1.95c-1.53.034-2.769-.82-2.786-1.86c0-1.065 1.202-1.932 2.733-1.958c1.522-.03 2.768.818 2.768 1.868zm10.555-.405c.182 1.03-.875 2.088-2.387 2.37c-1.485.271-2.861-.365-3.05-1.386c-.184-1.056.893-2.114 2.376-2.387c1.514-.263 2.868.356 3.061 1.403z\" fill=\"currentColor\"></path></svg>\n", "\t\t\t\t\t\t\t\t<span>14,426</span></div>\n", "\t\t\t\t\t\t<p class=\"mt-auto text-sm text-gray-500 md:text-base 2xl:text-lg\">State-of-the-art ML running directly in your browser</p>\n", "\t\t\t\t\t</a>\n", "\t\t\t\t\n", "\t\t\t\t<a href=\"/docs/smolagents\" class=\"relative col-span-1 flex h-48 flex-col rounded-[1.6rem] border border-gray-100 bg-white/80 p-5 shadow-sm transition-transform will-change-transform hover:translate-y-px hover:shadow-inner dark:border-gray-800 dark:bg-gray-900/60 sm:h-56 sm:p-6 2xl:h-72 2xl:p-10 lg:hover:translate-y-[calc(3rem+1px)]! lg:translate-y-12 \"><h4 class=\"leading-tight! flex items-center gap-1.5 text-lg font-bold sm:gap-2 sm:text-xl 2xl:text-2xl\"><div class=\"h-2 w-2 flex-none rounded-full bg-yellow-500\"></div>\n", "\t\t\t\t\t\t\tsmolagents</h4>\n", "\t\t\t\t\t\t<div class=\"flex items-center gap-1.5 text-lg\"><svg class=\"text-xs text-gray-700\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" aria-hidden=\"true\" focusable=\"false\" role=\"img\" width=\"1.03em\" height=\"1em\" preserveAspectRatio=\"xMidYMid meet\" viewBox=\"0 0 256 250\"><path d=\"M128.001 0C57.317 0 0 57.307 0 128.001c0 56.554 36.676 104.535 87.535 121.46c6.397 1.185 8.746-2.777 8.746-6.158c0-3.052-.12-13.135-.174-23.83c-35.61 7.742-43.124-15.103-43.124-15.103c-5.823-14.795-14.213-18.73-14.213-18.73c-11.613-7.944.876-7.78.876-7.78c12.853.902 19.621 13.19 19.621 13.19c11.417 19.568 29.945 13.911 37.249 10.64c1.149-8.272 4.466-13.92 8.127-17.116c-28.431-3.236-58.318-14.212-58.318-63.258c0-13.975 5-25.394 13.188-34.358c-1.329-3.224-5.71-16.242 1.24-33.874c0 0 10.749-3.44 35.21 13.121c10.21-2.836 21.16-4.258 32.038-4.307c10.878.049 21.837 1.47 32.066 4.307c24.431-16.56 35.165-13.12 35.165-13.12c6.967 17.63 2.584 30.65 1.255 33.873c8.207 8.964 13.173 20.383 13.173 34.358c0 49.163-29.944 59.988-58.447 63.157c4.591 3.972 8.682 11.762 8.682 23.704c0 17.126-.148 30.91-.148 35.126c0 3.407 2.304 7.398 8.792 6.14C219.37 232.5 256 184.537 256 128.002C256 57.307 198.691 0 128.001 0zm-80.06 182.34c-.282.636-1.283.827-2.194.39c-.929-.417-1.45-1.284-1.15-1.922c.276-.655 1.279-.838 2.205-.399c.93.418 1.46 1.293 1.139 1.931zm6.296 5.618c-.61.566-1.804.303-2.614-.591c-.837-.892-.994-2.086-.375-2.66c.63-.566 1.787-.301 2.626.591c.838.903 1 2.088.363 2.66zm4.32 7.188c-.785.545-2.067.034-2.86-1.104c-.784-1.138-.784-2.503.017-3.05c.795-.547 2.058-.055 2.861 1.075c.782 1.157.782 2.522-.019 3.08zm7.304 8.325c-.701.774-2.196.566-3.29-.49c-1.119-1.032-1.43-2.496-.726-3.27c.71-.776 2.213-.558 3.315.49c1.11 1.03 1.45 2.505.701 3.27zm9.442 2.81c-.31 1.003-1.75 1.459-3.199 1.033c-1.448-.439-2.395-1.613-2.103-2.626c.301-1.01 1.747-1.484 3.207-1.028c1.446.436 2.396 1.602 2.095 2.622zm10.744 1.193c.036 1.055-1.193 1.93-2.715 1.95c-1.53.034-2.769-.82-2.786-1.86c0-1.065 1.202-1.932 2.733-1.958c1.522-.03 2.768.818 2.768 1.868zm10.555-.405c.182 1.03-.875 2.088-2.387 2.37c-1.485.271-2.861-.365-3.05-1.386c-.184-1.056.893-2.114 2.376-2.387c1.514-.263 2.868.356 3.061 1.403z\" fill=\"currentColor\"></path></svg>\n", "\t\t\t\t\t\t\t\t<span>22,457</span></div>\n", "\t\t\t\t\t\t<p class=\"mt-auto text-sm text-gray-500 md:text-base 2xl:text-lg\">Smol library to build great agents in Python</p>\n", "\t\t\t\t\t</a>\n", "\t\t\t\t\n", "\t\t\t\t<a href=\"/docs/peft\" class=\"relative col-span-1 flex h-48 flex-col rounded-[1.6rem] border border-gray-100 bg-white/80 p-5 shadow-sm transition-transform will-change-transform hover:translate-y-px hover:shadow-inner dark:border-gray-800 dark:bg-gray-900/60 sm:h-56 sm:p-6 2xl:h-72 2xl:p-10  max-lg:hidden\"><h4 class=\"leading-tight! flex items-center gap-1.5 text-lg font-bold sm:gap-2 sm:text-xl 2xl:text-2xl\"><div class=\"h-2 w-2 flex-none rounded-full bg-red-500\"></div>\n", "\t\t\t\t\t\t\tPEFT</h4>\n", "\t\t\t\t\t\t<div class=\"flex items-center gap-1.5 text-lg\"><svg class=\"text-xs text-gray-700\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" aria-hidden=\"true\" focusable=\"false\" role=\"img\" width=\"1.03em\" height=\"1em\" preserveAspectRatio=\"xMidYMid meet\" viewBox=\"0 0 256 250\"><path d=\"M128.001 0C57.317 0 0 57.307 0 128.001c0 56.554 36.676 104.535 87.535 121.46c6.397 1.185 8.746-2.777 8.746-6.158c0-3.052-.12-13.135-.174-23.83c-35.61 7.742-43.124-15.103-43.124-15.103c-5.823-14.795-14.213-18.73-14.213-18.73c-11.613-7.944.876-7.78.876-7.78c12.853.902 19.621 13.19 19.621 13.19c11.417 19.568 29.945 13.911 37.249 10.64c1.149-8.272 4.466-13.92 8.127-17.116c-28.431-3.236-58.318-14.212-58.318-63.258c0-13.975 5-25.394 13.188-34.358c-1.329-3.224-5.71-16.242 1.24-33.874c0 0 10.749-3.44 35.21 13.121c10.21-2.836 21.16-4.258 32.038-4.307c10.878.049 21.837 1.47 32.066 4.307c24.431-16.56 35.165-13.12 35.165-13.12c6.967 17.63 2.584 30.65 1.255 33.873c8.207 8.964 13.173 20.383 13.173 34.358c0 49.163-29.944 59.988-58.447 63.157c4.591 3.972 8.682 11.762 8.682 23.704c0 17.126-.148 30.91-.148 35.126c0 3.407 2.304 7.398 8.792 6.14C219.37 232.5 256 184.537 256 128.002C256 57.307 198.691 0 128.001 0zm-80.06 182.34c-.282.636-1.283.827-2.194.39c-.929-.417-1.45-1.284-1.15-1.922c.276-.655 1.279-.838 2.205-.399c.93.418 1.46 1.293 1.139 1.931zm6.296 5.618c-.61.566-1.804.303-2.614-.591c-.837-.892-.994-2.086-.375-2.66c.63-.566 1.787-.301 2.626.591c.838.903 1 2.088.363 2.66zm4.32 7.188c-.785.545-2.067.034-2.86-1.104c-.784-1.138-.784-2.503.017-3.05c.795-.547 2.058-.055 2.861 1.075c.782 1.157.782 2.522-.019 3.08zm7.304 8.325c-.701.774-2.196.566-3.29-.49c-1.119-1.032-1.43-2.496-.726-3.27c.71-.776 2.213-.558 3.315.49c1.11 1.03 1.45 2.505.701 3.27zm9.442 2.81c-.31 1.003-1.75 1.459-3.199 1.033c-1.448-.439-2.395-1.613-2.103-2.626c.301-1.01 1.747-1.484 3.207-1.028c1.446.436 2.396 1.602 2.095 2.622zm10.744 1.193c.036 1.055-1.193 1.93-2.715 1.95c-1.53.034-2.769-.82-2.786-1.86c0-1.065 1.202-1.932 2.733-1.958c1.522-.03 2.768.818 2.768 1.868zm10.555-.405c.182 1.03-.875 2.088-2.387 2.37c-1.485.271-2.861-.365-3.05-1.386c-.184-1.056.893-2.114 2.376-2.387c1.514-.263 2.868.356 3.061 1.403z\" fill=\"currentColor\"></path></svg>\n", "\t\t\t\t\t\t\t\t<span>19,434</span></div>\n", "\t\t\t\t\t\t<p class=\"mt-auto text-sm text-gray-500 md:text-base 2xl:text-lg\">Parameter-efficient finetuning for large language models</p>\n", "\t\t\t\t\t</a>\n", "\t\t\t\t\n", "\t\t\t\t<a href=\"/docs/datasets\" class=\"relative col-span-1 flex h-48 flex-col rounded-[1.6rem] border border-gray-100 bg-white/80 p-5 shadow-sm transition-transform will-change-transform hover:translate-y-px hover:shadow-inner dark:border-gray-800 dark:bg-gray-900/60 sm:h-56 sm:p-6 2xl:h-72 2xl:p-10 lg:hover:translate-y-[calc(3rem+1px)]! lg:translate-y-12 max-lg:hidden\"><h4 class=\"leading-tight! flex items-center gap-1.5 text-lg font-bold sm:gap-2 sm:text-xl 2xl:text-2xl\"><div class=\"h-2 w-2 flex-none rounded-full bg-red-500\"></div>\n", "\t\t\t\t\t\t\tDatasets</h4>\n", "\t\t\t\t\t\t<div class=\"flex items-center gap-1.5 text-lg\"><svg class=\"text-xs text-gray-700\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" aria-hidden=\"true\" focusable=\"false\" role=\"img\" width=\"1.03em\" height=\"1em\" preserveAspectRatio=\"xMidYMid meet\" viewBox=\"0 0 256 250\"><path d=\"M128.001 0C57.317 0 0 57.307 0 128.001c0 56.554 36.676 104.535 87.535 121.46c6.397 1.185 8.746-2.777 8.746-6.158c0-3.052-.12-13.135-.174-23.83c-35.61 7.742-43.124-15.103-43.124-15.103c-5.823-14.795-14.213-18.73-14.213-18.73c-11.613-7.944.876-7.78.876-7.78c12.853.902 19.621 13.19 19.621 13.19c11.417 19.568 29.945 13.911 37.249 10.64c1.149-8.272 4.466-13.92 8.127-17.116c-28.431-3.236-58.318-14.212-58.318-63.258c0-13.975 5-25.394 13.188-34.358c-1.329-3.224-5.71-16.242 1.24-33.874c0 0 10.749-3.44 35.21 13.121c10.21-2.836 21.16-4.258 32.038-4.307c10.878.049 21.837 1.47 32.066 4.307c24.431-16.56 35.165-13.12 35.165-13.12c6.967 17.63 2.584 30.65 1.255 33.873c8.207 8.964 13.173 20.383 13.173 34.358c0 49.163-29.944 59.988-58.447 63.157c4.591 3.972 8.682 11.762 8.682 23.704c0 17.126-.148 30.91-.148 35.126c0 3.407 2.304 7.398 8.792 6.14C219.37 232.5 256 184.537 256 128.002C256 57.307 198.691 0 128.001 0zm-80.06 182.34c-.282.636-1.283.827-2.194.39c-.929-.417-1.45-1.284-1.15-1.922c.276-.655 1.279-.838 2.205-.399c.93.418 1.46 1.293 1.139 1.931zm6.296 5.618c-.61.566-1.804.303-2.614-.591c-.837-.892-.994-2.086-.375-2.66c.63-.566 1.787-.301 2.626.591c.838.903 1 2.088.363 2.66zm4.32 7.188c-.785.545-2.067.034-2.86-1.104c-.784-1.138-.784-2.503.017-3.05c.795-.547 2.058-.055 2.861 1.075c.782 1.157.782 2.522-.019 3.08zm7.304 8.325c-.701.774-2.196.566-3.29-.49c-1.119-1.032-1.43-2.496-.726-3.27c.71-.776 2.213-.558 3.315.49c1.11 1.03 1.45 2.505.701 3.27zm9.442 2.81c-.31 1.003-1.75 1.459-3.199 1.033c-1.448-.439-2.395-1.613-2.103-2.626c.301-1.01 1.747-1.484 3.207-1.028c1.446.436 2.396 1.602 2.095 2.622zm10.744 1.193c.036 1.055-1.193 1.93-2.715 1.95c-1.53.034-2.769-.82-2.786-1.86c0-1.065 1.202-1.932 2.733-1.958c1.522-.03 2.768.818 2.768 1.868zm10.555-.405c.182 1.03-.875 2.088-2.387 2.37c-1.485.271-2.861-.365-3.05-1.386c-.184-1.056.893-2.114 2.376-2.387c1.514-.263 2.868.356 3.061 1.403z\" fill=\"currentColor\"></path></svg>\n", "\t\t\t\t\t\t\t\t<span>20,577</span></div>\n", "\t\t\t\t\t\t<p class=\"mt-auto text-sm text-gray-500 md:text-base 2xl:text-lg\">Access &amp; share datasets for any ML tasks</p>\n", "\t\t\t\t\t</a>\n", "\t\t\t\t\n", "\t\t\t\t<a href=\"/docs/text-generation-inference\" class=\"relative col-span-1 flex h-48 flex-col rounded-[1.6rem] border border-gray-100 bg-white/80 p-5 shadow-sm transition-transform will-change-transform hover:translate-y-px hover:shadow-inner dark:border-gray-800 dark:bg-gray-900/60 sm:h-56 sm:p-6 2xl:h-72 2xl:p-10  max-lg:hidden\"><h4 class=\"leading-tight! flex items-center gap-1.5 text-lg font-bold sm:gap-2 sm:text-xl 2xl:text-2xl\"><div class=\"h-2 w-2 flex-none rounded-full bg-blue-500\"></div>\n", "\t\t\t\t\t\t\tText Generation Inference</h4>\n", "\t\t\t\t\t\t<div class=\"flex items-center gap-1.5 text-lg\"><svg class=\"text-xs text-gray-700\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" aria-hidden=\"true\" focusable=\"false\" role=\"img\" width=\"1.03em\" height=\"1em\" preserveAspectRatio=\"xMidYMid meet\" viewBox=\"0 0 256 250\"><path d=\"M128.001 0C57.317 0 0 57.307 0 128.001c0 56.554 36.676 104.535 87.535 121.46c6.397 1.185 8.746-2.777 8.746-6.158c0-3.052-.12-13.135-.174-23.83c-35.61 7.742-43.124-15.103-43.124-15.103c-5.823-14.795-14.213-18.73-14.213-18.73c-11.613-7.944.876-7.78.876-7.78c12.853.902 19.621 13.19 19.621 13.19c11.417 19.568 29.945 13.911 37.249 10.64c1.149-8.272 4.466-13.92 8.127-17.116c-28.431-3.236-58.318-14.212-58.318-63.258c0-13.975 5-25.394 13.188-34.358c-1.329-3.224-5.71-16.242 1.24-33.874c0 0 10.749-3.44 35.21 13.121c10.21-2.836 21.16-4.258 32.038-4.307c10.878.049 21.837 1.47 32.066 4.307c24.431-16.56 35.165-13.12 35.165-13.12c6.967 17.63 2.584 30.65 1.255 33.873c8.207 8.964 13.173 20.383 13.173 34.358c0 49.163-29.944 59.988-58.447 63.157c4.591 3.972 8.682 11.762 8.682 23.704c0 17.126-.148 30.91-.148 35.126c0 3.407 2.304 7.398 8.792 6.14C219.37 232.5 256 184.537 256 128.002C256 57.307 198.691 0 128.001 0zm-80.06 182.34c-.282.636-1.283.827-2.194.39c-.929-.417-1.45-1.284-1.15-1.922c.276-.655 1.279-.838 2.205-.399c.93.418 1.46 1.293 1.139 1.931zm6.296 5.618c-.61.566-1.804.303-2.614-.591c-.837-.892-.994-2.086-.375-2.66c.63-.566 1.787-.301 2.626.591c.838.903 1 2.088.363 2.66zm4.32 7.188c-.785.545-2.067.034-2.86-1.104c-.784-1.138-.784-2.503.017-3.05c.795-.547 2.058-.055 2.861 1.075c.782 1.157.782 2.522-.019 3.08zm7.304 8.325c-.701.774-2.196.566-3.29-.49c-1.119-1.032-1.43-2.496-.726-3.27c.71-.776 2.213-.558 3.315.49c1.11 1.03 1.45 2.505.701 3.27zm9.442 2.81c-.31 1.003-1.75 1.459-3.199 1.033c-1.448-.439-2.395-1.613-2.103-2.626c.301-1.01 1.747-1.484 3.207-1.028c1.446.436 2.396 1.602 2.095 2.622zm10.744 1.193c.036 1.055-1.193 1.93-2.715 1.95c-1.53.034-2.769-.82-2.786-1.86c0-1.065 1.202-1.932 2.733-1.958c1.522-.03 2.768.818 2.768 1.868zm10.555-.405c.182 1.03-.875 2.088-2.387 2.37c-1.485.271-2.861-.365-3.05-1.386c-.184-1.056.893-2.114 2.376-2.387c1.514-.263 2.868.356 3.061 1.403z\" fill=\"currentColor\"></path></svg>\n", "\t\t\t\t\t\t\t\t<span>10,458</span></div>\n", "\t\t\t\t\t\t<p class=\"mt-auto text-sm text-gray-500 md:text-base 2xl:text-lg\">Serve language models with TGI optimized toolkit</p>\n", "\t\t\t\t\t</a>\n", "\t\t\t\t\n", "\t\t\t\t<a href=\"/docs/accelerate\" class=\"relative col-span-1 flex h-48 flex-col rounded-[1.6rem] border border-gray-100 bg-white/80 p-5 shadow-sm transition-transform will-change-transform hover:translate-y-px hover:shadow-inner dark:border-gray-800 dark:bg-gray-900/60 sm:h-56 sm:p-6 2xl:h-72 2xl:p-10 lg:hover:translate-y-[calc(3rem+1px)]! lg:translate-y-12 max-lg:hidden\"><h4 class=\"leading-tight! flex items-center gap-1.5 text-lg font-bold sm:gap-2 sm:text-xl 2xl:text-2xl\"><div class=\"h-2 w-2 flex-none rounded-full bg-pink-500\"></div>\n", "\t\t\t\t\t\t\tAccelerate</h4>\n", "\t\t\t\t\t\t<div class=\"flex items-center gap-1.5 text-lg\"><svg class=\"text-xs text-gray-700\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" aria-hidden=\"true\" focusable=\"false\" role=\"img\" width=\"1.03em\" height=\"1em\" preserveAspectRatio=\"xMidYMid meet\" viewBox=\"0 0 256 250\"><path d=\"M128.001 0C57.317 0 0 57.307 0 128.001c0 56.554 36.676 104.535 87.535 121.46c6.397 1.185 8.746-2.777 8.746-6.158c0-3.052-.12-13.135-.174-23.83c-35.61 7.742-43.124-15.103-43.124-15.103c-5.823-14.795-14.213-18.73-14.213-18.73c-11.613-7.944.876-7.78.876-7.78c12.853.902 19.621 13.19 19.621 13.19c11.417 19.568 29.945 13.911 37.249 10.64c1.149-8.272 4.466-13.92 8.127-17.116c-28.431-3.236-58.318-14.212-58.318-63.258c0-13.975 5-25.394 13.188-34.358c-1.329-3.224-5.71-16.242 1.24-33.874c0 0 10.749-3.44 35.21 13.121c10.21-2.836 21.16-4.258 32.038-4.307c10.878.049 21.837 1.47 32.066 4.307c24.431-16.56 35.165-13.12 35.165-13.12c6.967 17.63 2.584 30.65 1.255 33.873c8.207 8.964 13.173 20.383 13.173 34.358c0 49.163-29.944 59.988-58.447 63.157c4.591 3.972 8.682 11.762 8.682 23.704c0 17.126-.148 30.91-.148 35.126c0 3.407 2.304 7.398 8.792 6.14C219.37 232.5 256 184.537 256 128.002C256 57.307 198.691 0 128.001 0zm-80.06 182.34c-.282.636-1.283.827-2.194.39c-.929-.417-1.45-1.284-1.15-1.922c.276-.655 1.279-.838 2.205-.399c.93.418 1.46 1.293 1.139 1.931zm6.296 5.618c-.61.566-1.804.303-2.614-.591c-.837-.892-.994-2.086-.375-2.66c.63-.566 1.787-.301 2.626.591c.838.903 1 2.088.363 2.66zm4.32 7.188c-.785.545-2.067.034-2.86-1.104c-.784-1.138-.784-2.503.017-3.05c.795-.547 2.058-.055 2.861 1.075c.782 1.157.782 2.522-.019 3.08zm7.304 8.325c-.701.774-2.196.566-3.29-.49c-1.119-1.032-1.43-2.496-.726-3.27c.71-.776 2.213-.558 3.315.49c1.11 1.03 1.45 2.505.701 3.27zm9.442 2.81c-.31 1.003-1.75 1.459-3.199 1.033c-1.448-.439-2.395-1.613-2.103-2.626c.301-1.01 1.747-1.484 3.207-1.028c1.446.436 2.396 1.602 2.095 2.622zm10.744 1.193c.036 1.055-1.193 1.93-2.715 1.95c-1.53.034-2.769-.82-2.786-1.86c0-1.065 1.202-1.932 2.733-1.958c1.522-.03 2.768.818 2.768 1.868zm10.555-.405c.182 1.03-.875 2.088-2.387 2.37c-1.485.271-2.861-.365-3.05-1.386c-.184-1.056.893-2.114 2.376-2.387c1.514-.263 2.868.356 3.061 1.403z\" fill=\"currentColor\"></path></svg>\n", "\t\t\t\t\t\t\t\t<span>9,081</span></div>\n", "\t\t\t\t\t\t<p class=\"mt-auto text-sm text-gray-500 md:text-base 2xl:text-lg\">Train PyTorch models with multi-GPU, TPU, mixed precision</p>\n", "\t\t\t\t\t</a>\n", "\t\t\t<div class=\"bg-linear-to-b pointer-events-none absolute bottom-0 left-0 z-2 h-1/2 w-full from-transparent to-white dark:to-gray-950\"></div></div></div>\n", "\t<div class=\"container relative z-2\"><img src=\"/front/assets/homepage/hugs.svg\" class=\"pointer-events-none w-full select-none max-lg:hidden\" alt=\"Pile of Hugging Face logos\">\n", "\t\t<img src=\"/front/assets/homepage/hugs-mobile.svg\" class=\"pointer-events-none w-full select-none lg:hidden\" alt=\"Pile of Hugging Face logos\"></div></main>\n", "\n", "\t<footer class=\"border-t border-gray-100\"><div class=\"container pb-32 pt-12\"><div class=\"grid gap-8 sm:grid-cols-2 md:grid-cols-5\"><div class=\"sm:col-span-2 md:col-span-1\"><div class=\"SVELTE_HYDRATER contents\" data-target=\"ThemeSwitcher\" data-props=\"{&quot;theme&quot;:&quot;system&quot;,&quot;isLoggedIn&quot;:false}\">\n", "<div class=\"relative inline-block \">\n", "\t<button class=\"rounded-full border border-gray-100 pl-2 py-1 pr-2.5  flex items-center text-sm text-gray-500 bg-white hover:bg-purple-50 hover:border-purple-200 dark:hover:bg-gray-800 dark:hover:border-gray-950 dark:border-gray-800 \" type=\"button\">\n", "\t\t<svg class=\"mr-1.5 text-gray-500\" xmlns=\"http://www.w3.org/2000/svg\" aria-hidden=\"true\" fill=\"currentColor\" focusable=\"false\" role=\"img\" width=\"1em\" height=\"1em\" preserveAspectRatio=\"xMidYMid meet\" viewBox=\"0 0 32 32\"><path d=\"M29 25H3a1 1 0 1 0 0 2h26a1 1 0 1 0 0-2Z\" fill=\"currentColor\"></path><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M6 22.5h20a2 2 0 0 0 2-2V7a2 2 0 0 0-2-2H6a2 2 0 0 0-2 2v13.5a2 2 0 0 0 2 2ZM7 7a1 1 0 0 0-1 1v11a1 1 0 0 0 1 1h18a1 1 0 0 0 1-1V8a1 1 0 0 0-1-1H7Z\" fill=\"currentColor\"></path><path d=\"M6 8a1 1 0 0 1 1-1h18a1 1 0 0 1 1 1v11a1 1 0 0 1-1 1H7a1 1 0 0 1-1-1V8Z\" fill=\"currentColor\" fill-opacity=\".4\"></path><path d=\"M29 25H3a1 1 0 1 0 0 2h26a1 1 0 1 0 0-2Z\" fill=\"currentColor\"></path></svg>\n", "\t\t\tSystem theme\n", "\t\t</button>\n", "\t\n", "\t\n", "\t</div></div></div>\n", "\t\t\t<div><div class=\"mb-4 text-lg font-semibold\">Website</div>\n", "\t\t\t\t<ul class=\"space-y-1 text-gray-600 md:space-y-2\"><li><a class=\"hover:underline\" href=\"/models\">Models </a></li>\n", "\t\t\t\t\t<li><a class=\"hover:underline\" href=\"/datasets\">Datasets </a></li>\n", "\t\t\t\t\t<li><a class=\"hover:underline\" href=\"/spaces\">Spaces </a></li>\n", "\t\t\t\t\t<li class=\"flex items-center\"><a class=\"group inline-flex gap-x-2.5\" href=\"/changelog\"><span class=\"group-hover:underline\">Changelog</span>\n", "\t\t\t\t\t\t\t</a></li>\n", "\t\t\t\t\t<li><a class=\"hover:underline\" href=\"https://endpoints.huggingface.co\" target=\"_blank\">Inference Endpoints </a></li>\n", "\t\t\t\t\t<li><a class=\"hover:underline\" href=\"/chat\">HuggingChat </a></li></ul></div>\n", "\t\t\t<div><div class=\"mb-4 text-lg font-semibold\">Company</div>\n", "\t\t\t\t<ul class=\"space-y-1 text-gray-600 md:space-y-2\"><li><a class=\"hover:underline\" href=\"/huggingface\">About </a></li>\n", "\t\t\t\t\t<li><a class=\"hover:underline\" href=\"/brand\">Brand assets </a></li>\n", "\t\t\t\t\t<li><a class=\"hover:underline\" href=\"/terms-of-service\">Terms of service </a></li>\n", "\t\t\t\t\t<li><a class=\"hover:underline\" href=\"/privacy\">Privacy </a></li>\n", "\t\t\t\t\t<li><a class=\"hover:underline\" href=\"https://apply.workable.com/huggingface/\">Jobs </a></li>\n", "\t\t\t\t\t<li><a class=\"hover:underline\" href=\"mailto:<EMAIL>\">Press </a></li></ul></div>\n", "\t\t\t<div><div class=\"mb-4 text-lg font-semibold\">Resources</div>\n", "\t\t\t\t<ul class=\"space-y-1 text-gray-600 md:space-y-2\"><li><a class=\"hover:underline\" href=\"/learn\">Learn </a></li>\n", "\t\t\t\t\t<li><a class=\"hover:underline\" href=\"/docs\">Documentation </a></li>\n", "\t\t\t\t\t<li><a class=\"hover:underline\" href=\"/blog\">Blog </a></li>\n", "\t\t\t\t\t<li><a class=\"hover:underline\" href=\"https://discuss.huggingface.co\">Forum </a></li>\n", "\t\t\t\t\t<li><a class=\"hover:underline\" href=\"https://status.huggingface.co/\">Service Status </a></li></ul></div>\n", "\t\t\t<div><div class=\"mb-4 text-lg font-semibold\">Social</div>\n", "\t\t\t\t<ul class=\"space-y-1 text-gray-600 md:space-y-2\"><li><a class=\"hover:underline\" href=\"https://github.com/huggingface\">GitHub </a></li>\n", "\t\t\t\t\t<li><a class=\"hover:underline\" href=\"https://twitter.com/huggingface\">Twitter </a></li>\n", "\t\t\t\t\t<li><a class=\"hover:underline\" href=\"https://www.linkedin.com/company/huggingface/\">LinkedIn </a></li>\n", "\t\t\t\t\t<li><a class=\"hover:underline\" href=\"/join/discord\">Discord </a></li>\n", "\t\t\t\t\t</ul></div></div></div></footer></div>\n", "\n", "\t\t<script>\n", "\t\t\timport(\"\\/front\\/build\\/kube-37f3ff5\\/index.js\");\n", "\t\t\twindow.moonSha = \"kube-37f3ff5\\/\";\n", "\t\t\twindow.__hf_deferred = {};\n", "\t\t</script>\n", "\n", "\t\t<!-- Stripe -->\n", "\t\t<script>\n", "\t\t\tif ([\"hf.co\", \"huggingface.co\"].includes(window.location.hostname)) {\n", "\t\t\t\tconst script = document.createElement(\"script\");\n", "\t\t\t\tscript.src = \"https://js.stripe.com/v3/\";\n", "\t\t\t\tscript.async = true;\n", "\t\t\t\tdocument.head.appendChild(script);\n", "\t\t\t}\n", "\t\t</script>\n", "\t</body>\n", "</html>\n"]}], "source": ["!curl https://huggingface.co/"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["原始文本: 'My name is <PERSON><PERSON><PERSON> jam<PERSON>'\n", "==================================================\n"]}], "source": ["import torch\n", "import numpy as np\n", "from transformers import (\n", "    DPRQuestionEncoder, DPRQuestionEncoderTokenizer,\n", "    <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>\n", ")\n", "from transformers import BertTokenizer, BertModel\n", "\n", "# 示例文本\n", "text = \"My name is <PERSON><PERSON><PERSON> <PERSON><PERSON>\"\n", "print(f\"原始文本: '{text}'\")\n", "print(\"=\" * 50)"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["The tokenizer class you load from this checkpoint is not the same type as the class this function is called from. It may result in unexpected tokenization. \n", "The tokenizer class you load from this checkpoint is 'BertTokenizer'. \n", "The class this function is called from is 'DPRQuestionEncoderTokenizer'.\n", "You are using a model of type bert to instantiate a model of type dpr. This is not supported for all configurations of models and can yield errors.\n", "/Volumes/Data/miniconda/miniconda3/envs/tableqa/lib/python3.8/site-packages/transformers/modeling_utils.py:367: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.\n", "  return torch.load(checkpoint_file, map_location=\"cpu\")\n", "Some weights of the model checkpoint at /Users/<USER>/Documents/Code/Experiment/robust-tableqa-main/dir were not used when initializing DPRQuestionEncoder: ['pooler.dense.bias', 'encoder.layer.4.attention.self.key.bias', 'encoder.layer.11.attention.output.dense.bias', 'encoder.layer.8.output.dense.weight', 'encoder.layer.3.attention.self.key.weight', 'embeddings.LayerNorm.weight', 'encoder.layer.7.attention.output.LayerNorm.bias', 'encoder.layer.8.attention.output.LayerNorm.bias', 'encoder.layer.8.output.dense.bias', 'encoder.layer.9.output.LayerNorm.bias', 'encoder.layer.11.attention.self.value.weight', 'encoder.layer.5.output.LayerNorm.bias', 'encoder.layer.0.attention.output.dense.bias', 'encoder.layer.10.attention.self.value.weight', 'encoder.layer.11.output.LayerNorm.weight', 'encoder.layer.2.attention.output.LayerNorm.bias', 'encoder.layer.1.attention.output.LayerNorm.bias', 'embeddings.token_type_embeddings.weight', 'embeddings.position_embeddings.weight', 'encoder.layer.3.attention.output.dense.weight', 'encoder.layer.9.attention.self.key.weight', 'encoder.layer.3.attention.output.dense.bias', 'encoder.layer.8.attention.self.value.weight', 'embeddings.word_embeddings.weight', 'encoder.layer.9.output.dense.bias', 'encoder.layer.6.attention.self.query.weight', 'encoder.layer.10.attention.self.key.weight', 'encoder.layer.9.attention.self.query.weight', 'encoder.layer.9.intermediate.dense.bias', 'encoder.layer.11.intermediate.dense.bias', 'encoder.layer.10.output.LayerNorm.weight', 'encoder.layer.2.attention.self.key.weight', 'encoder.layer.0.attention.self.value.bias', 'encoder.layer.10.attention.output.LayerNorm.bias', 'encoder.layer.10.intermediate.dense.weight', 'encoder.layer.7.intermediate.dense.weight', 'encoder.layer.7.attention.self.value.weight', 'encoder.layer.1.output.dense.weight', 'encoder.layer.2.output.dense.weight', 'encoder.layer.10.output.LayerNorm.bias', 'encoder.layer.11.attention.output.LayerNorm.bias', 'encoder.layer.10.attention.self.value.bias', 'encoder.layer.10.attention.output.dense.bias', 'encoder.layer.3.attention.self.query.weight', 'encoder.layer.9.attention.output.LayerNorm.bias', 'encoder.layer.9.output.LayerNorm.weight', 'encoder.layer.4.attention.output.LayerNorm.weight', 'encoder.layer.2.output.dense.bias', 'encoder.layer.1.attention.self.key.bias', 'encoder.layer.7.intermediate.dense.bias', 'encoder.layer.7.attention.self.key.bias', 'encoder.layer.6.output.dense.bias', 'encoder.layer.10.attention.self.key.bias', 'encoder.layer.7.output.dense.bias', 'encoder.layer.7.output.LayerNorm.weight', 'encoder.layer.5.attention.output.dense.bias', 'encoder.layer.6.output.dense.weight', 'encoder.layer.9.attention.output.dense.weight', 'encoder.layer.9.output.dense.weight', 'encoder.layer.1.attention.self.query.weight', 'encoder.layer.11.output.dense.bias', 'encoder.layer.3.attention.output.LayerNorm.weight', 'encoder.layer.11.attention.self.key.weight', 'encoder.layer.6.attention.self.key.weight', 'encoder.layer.8.attention.self.query.weight', 'encoder.layer.8.output.LayerNorm.weight', 'encoder.layer.7.attention.output.dense.weight', 'encoder.layer.11.attention.self.query.bias', 'encoder.layer.6.attention.output.dense.weight', 'encoder.layer.2.attention.self.query.weight', 'encoder.layer.2.attention.output.dense.weight', 'encoder.layer.9.attention.self.value.weight', 'encoder.layer.1.attention.self.value.weight', 'encoder.layer.4.attention.output.LayerNorm.bias', 'encoder.layer.8.attention.output.LayerNorm.weight', 'pooler.dense.weight', 'encoder.layer.0.output.dense.bias', 'encoder.layer.1.attention.self.query.bias', 'encoder.layer.3.intermediate.dense.weight', 'encoder.layer.3.attention.self.value.bias', 'encoder.layer.3.output.LayerNorm.bias', 'encoder.layer.10.attention.output.dense.weight', 'encoder.layer.5.output.dense.bias', 'encoder.layer.11.intermediate.dense.weight', 'encoder.layer.11.attention.self.key.bias', 'encoder.layer.0.attention.output.LayerNorm.weight', 'encoder.layer.2.intermediate.dense.weight', 'encoder.layer.8.attention.self.value.bias', 'encoder.layer.2.attention.self.value.weight', 'encoder.layer.0.output.dense.weight', 'encoder.layer.5.output.dense.weight', 'encoder.layer.7.output.LayerNorm.bias', 'encoder.layer.1.output.dense.bias', 'encoder.layer.0.output.LayerNorm.weight', 'encoder.layer.3.intermediate.dense.bias', 'encoder.layer.5.intermediate.dense.weight', 'encoder.layer.4.attention.self.query.bias', 'encoder.layer.8.attention.self.key.bias', 'encoder.layer.3.attention.self.query.bias', 'encoder.layer.9.attention.self.query.bias', 'encoder.layer.3.output.LayerNorm.weight', 'encoder.layer.5.output.LayerNorm.weight', 'encoder.layer.5.attention.self.key.bias', 'encoder.layer.4.intermediate.dense.weight', 'encoder.layer.2.output.LayerNorm.weight', 'encoder.layer.3.attention.output.LayerNorm.bias', 'encoder.layer.11.attention.self.query.weight', 'encoder.layer.10.attention.self.query.weight', 'encoder.layer.2.attention.self.key.bias', 'encoder.layer.0.output.LayerNorm.bias', 'encoder.layer.11.output.LayerNorm.bias', 'encoder.layer.11.output.dense.weight', 'encoder.layer.10.output.dense.weight', 'encoder.layer.8.output.LayerNorm.bias', 'encoder.layer.3.output.dense.bias', 'encoder.layer.5.attention.self.value.weight', 'encoder.layer.11.attention.self.value.bias', 'encoder.layer.0.intermediate.dense.weight', 'encoder.layer.5.attention.output.LayerNorm.bias', 'encoder.layer.3.attention.self.value.weight', 'encoder.layer.4.output.LayerNorm.weight', 'encoder.layer.6.output.LayerNorm.weight', 'encoder.layer.7.attention.self.key.weight', 'embeddings.LayerNorm.bias', 'encoder.layer.7.attention.output.dense.bias', 'encoder.layer.1.attention.self.key.weight', 'encoder.layer.1.intermediate.dense.bias', 'encoder.layer.5.attention.self.query.bias', 'encoder.layer.6.output.LayerNorm.bias', 'encoder.layer.4.attention.output.dense.weight', 'encoder.layer.0.attention.self.query.bias', 'encoder.layer.2.output.LayerNorm.bias', 'encoder.layer.6.intermediate.dense.bias', 'encoder.layer.1.attention.output.dense.weight', 'encoder.layer.0.attention.self.key.bias', 'encoder.layer.0.attention.self.key.weight', 'encoder.layer.4.output.LayerNorm.bias', 'encoder.layer.6.attention.self.value.bias', 'encoder.layer.4.intermediate.dense.bias', 'encoder.layer.4.output.dense.bias', 'encoder.layer.8.attention.output.dense.bias', 'encoder.layer.7.attention.self.value.bias', 'encoder.layer.8.attention.self.query.bias', 'encoder.layer.7.attention.self.query.weight', 'encoder.layer.7.attention.self.query.bias', 'encoder.layer.4.attention.self.value.bias', 'encoder.layer.6.attention.self.value.weight', 'encoder.layer.9.attention.self.key.bias', 'encoder.layer.7.output.dense.weight', 'encoder.layer.4.attention.output.dense.bias', 'encoder.layer.6.attention.self.key.bias', 'encoder.layer.9.attention.output.LayerNorm.weight', 'encoder.layer.10.attention.output.LayerNorm.weight', 'encoder.layer.8.attention.output.dense.weight', 'encoder.layer.2.attention.self.value.bias', 'encoder.layer.5.attention.self.query.weight', 'encoder.layer.5.attention.self.value.bias', 'encoder.layer.6.attention.output.dense.bias', 'encoder.layer.5.attention.output.LayerNorm.weight', 'encoder.layer.1.output.LayerNorm.weight', 'encoder.layer.6.attention.self.query.bias', 'encoder.layer.10.intermediate.dense.bias', 'encoder.layer.6.attention.output.LayerNorm.bias', 'encoder.layer.2.attention.self.query.bias', 'encoder.layer.7.attention.output.LayerNorm.weight', 'encoder.layer.1.attention.self.value.bias', 'encoder.layer.8.intermediate.dense.bias', 'encoder.layer.0.attention.output.dense.weight', 'encoder.layer.5.intermediate.dense.bias', 'encoder.layer.2.attention.output.LayerNorm.weight', 'encoder.layer.5.attention.output.dense.weight', 'encoder.layer.10.attention.self.query.bias', 'encoder.layer.5.attention.self.key.weight', 'encoder.layer.8.intermediate.dense.weight', 'encoder.layer.3.attention.self.key.bias', 'encoder.layer.0.intermediate.dense.bias', 'encoder.layer.6.intermediate.dense.weight', 'encoder.layer.10.output.dense.bias', 'encoder.layer.0.attention.self.value.weight', 'encoder.layer.4.attention.self.key.weight', 'encoder.layer.1.output.LayerNorm.bias', 'encoder.layer.2.attention.output.dense.bias', 'encoder.layer.9.attention.self.value.bias', 'encoder.layer.9.intermediate.dense.weight', 'encoder.layer.1.intermediate.dense.weight', 'encoder.layer.9.attention.output.dense.bias', 'encoder.layer.0.attention.output.LayerNorm.bias', 'encoder.layer.4.attention.self.query.weight', 'encoder.layer.0.attention.self.query.weight', 'encoder.layer.1.attention.output.LayerNorm.weight', 'encoder.layer.8.attention.self.key.weight', 'encoder.layer.4.output.dense.weight', 'encoder.layer.3.output.dense.weight', 'encoder.layer.11.attention.output.dense.weight', 'encoder.layer.4.attention.self.value.weight', 'encoder.layer.6.attention.output.LayerNorm.weight', 'embeddings.position_ids', 'encoder.layer.1.attention.output.dense.bias', 'encoder.layer.11.attention.output.LayerNorm.weight', 'encoder.layer.2.intermediate.dense.bias']\n", "- This IS expected if you are initializing DPRQuestionEncoder from the checkpoint of a model trained on another task or with another architecture (e.g. initializing a BertForSequenceClassification model from a BertForPreTraining model).\n", "- This IS NOT expected if you are initializing DPRQuestionEncoder from the checkpoint of a model that you expect to be exactly identical (initializing a BertForSequenceClassification model from a BertForSequenceClassification model).\n", "Some weights of DPRQuestionEncoder were not initialized from the model checkpoint at /Users/<USER>/Documents/Code/Experiment/robust-tableqa-main/dir and are newly initialized: ['bert_model.encoder.layer.2.attention.self.value.weight', 'bert_model.encoder.layer.6.attention.self.value.bias', 'bert_model.encoder.layer.0.attention.output.LayerNorm.weight', 'bert_model.encoder.layer.7.output.dense.weight', 'bert_model.encoder.layer.6.attention.self.query.weight', 'bert_model.encoder.layer.1.attention.output.LayerNorm.weight', 'bert_model.encoder.layer.1.output.dense.weight', 'bert_model.encoder.layer.11.intermediate.dense.bias', 'bert_model.encoder.layer.10.attention.self.value.weight', 'bert_model.encoder.layer.10.attention.self.key.bias', 'bert_model.encoder.layer.10.attention.output.dense.weight', 'bert_model.encoder.layer.11.attention.self.value.weight', 'bert_model.encoder.layer.0.attention.self.value.weight', 'bert_model.encoder.layer.0.attention.output.LayerNorm.bias', 'bert_model.encoder.layer.9.attention.self.value.weight', 'bert_model.encoder.layer.9.attention.output.dense.bias', 'bert_model.encoder.layer.5.attention.self.key.weight', 'bert_model.encoder.layer.3.attention.self.key.weight', 'bert_model.encoder.layer.1.attention.self.value.weight', 'bert_model.encoder.layer.0.output.dense.weight', 'bert_model.encoder.layer.3.attention.output.dense.bias', 'bert_model.encoder.layer.1.attention.self.key.weight', 'bert_model.encoder.layer.5.attention.output.LayerNorm.weight', 'bert_model.encoder.layer.6.attention.self.key.weight', 'bert_model.encoder.layer.9.output.LayerNorm.weight', 'bert_model.encoder.layer.8.output.dense.bias', 'bert_model.encoder.layer.11.attention.self.key.bias', 'bert_model.encoder.layer.4.attention.self.key.weight', 'bert_model.encoder.layer.0.intermediate.dense.bias', 'bert_model.encoder.layer.4.attention.self.value.bias', 'bert_model.embeddings.token_type_embeddings.weight', 'bert_model.encoder.layer.10.intermediate.dense.bias', 'bert_model.encoder.layer.6.attention.output.dense.bias', 'bert_model.encoder.layer.5.attention.self.query.weight', 'bert_model.encoder.layer.8.attention.output.LayerNorm.weight', 'bert_model.encoder.layer.10.attention.output.LayerNorm.weight', 'bert_model.encoder.layer.11.attention.output.LayerNorm.bias', 'bert_model.encoder.layer.2.attention.output.dense.bias', 'bert_model.encoder.layer.10.attention.self.query.weight', 'bert_model.encoder.layer.3.output.LayerNorm.bias', 'bert_model.encoder.layer.8.attention.output.dense.bias', 'bert_model.encoder.layer.0.attention.self.value.bias', 'bert_model.encoder.layer.1.intermediate.dense.weight', 'bert_model.encoder.layer.5.output.LayerNorm.bias', 'bert_model.encoder.layer.0.output.LayerNorm.weight', 'bert_model.encoder.layer.5.output.dense.weight', 'bert_model.encoder.layer.7.attention.output.LayerNorm.weight', 'bert_model.encoder.layer.7.attention.self.value.bias', 'bert_model.encoder.layer.4.output.LayerNorm.bias', 'bert_model.encoder.layer.8.attention.self.query.weight', 'bert_model.encoder.layer.7.attention.self.key.bias', 'bert_model.encoder.layer.3.attention.self.value.bias', 'bert_model.encoder.layer.2.output.dense.bias', 'bert_model.encoder.layer.4.attention.output.dense.bias', 'bert_model.encoder.layer.0.attention.self.query.bias', 'bert_model.encoder.layer.2.output.LayerNorm.weight', 'bert_model.encoder.layer.4.attention.output.LayerNorm.weight', 'bert_model.encoder.layer.10.output.dense.bias', 'bert_model.encoder.layer.4.attention.self.query.bias', 'bert_model.encoder.layer.4.attention.self.value.weight', 'bert_model.encoder.layer.5.attention.self.key.bias', 'bert_model.encoder.layer.7.attention.self.value.weight', 'bert_model.encoder.layer.0.attention.self.key.weight', 'bert_model.encoder.layer.1.attention.output.LayerNorm.bias', 'bert_model.encoder.layer.2.intermediate.dense.bias', 'bert_model.encoder.layer.3.attention.output.LayerNorm.weight', 'bert_model.encoder.layer.2.output.LayerNorm.bias', 'bert_model.encoder.layer.0.attention.self.key.bias', 'bert_model.encoder.layer.9.attention.self.key.bias', 'bert_model.encoder.layer.9.attention.self.value.bias', 'bert_model.encoder.layer.4.intermediate.dense.weight', 'bert_model.encoder.layer.9.attention.output.LayerNorm.bias', 'bert_model.encoder.layer.4.output.LayerNorm.weight', 'bert_model.encoder.layer.6.attention.self.value.weight', 'bert_model.encoder.layer.1.attention.self.key.bias', 'bert_model.encoder.layer.6.output.dense.weight', 'bert_model.encoder.layer.2.attention.output.LayerNorm.bias', 'bert_model.encoder.layer.7.output.LayerNorm.weight', 'bert_model.encoder.layer.3.attention.self.query.bias', 'bert_model.encoder.layer.7.attention.output.dense.weight', 'bert_model.encoder.layer.8.attention.self.value.bias', 'bert_model.encoder.layer.8.intermediate.dense.bias', 'bert_model.encoder.layer.5.output.dense.bias', 'bert_model.encoder.layer.2.attention.self.key.bias', 'bert_model.encoder.layer.10.attention.self.key.weight', 'bert_model.encoder.layer.2.attention.output.LayerNorm.weight', 'bert_model.encoder.layer.8.attention.self.value.weight', 'bert_model.encoder.layer.9.output.dense.weight', 'bert_model.encoder.layer.5.attention.output.LayerNorm.bias', 'bert_model.encoder.layer.0.intermediate.dense.weight', 'bert_model.encoder.layer.6.intermediate.dense.bias', 'bert_model.encoder.layer.11.output.LayerNorm.bias', 'bert_model.encoder.layer.0.output.dense.bias', 'bert_model.encoder.layer.8.attention.self.key.weight', 'bert_model.encoder.layer.1.output.LayerNorm.bias', 'bert_model.encoder.layer.11.attention.self.value.bias', 'bert_model.encoder.layer.0.output.LayerNorm.bias', 'bert_model.encoder.layer.1.attention.self.query.bias', 'bert_model.encoder.layer.5.intermediate.dense.bias', 'bert_model.encoder.layer.2.attention.self.key.weight', 'bert_model.encoder.layer.7.intermediate.dense.weight', 'bert_model.encoder.layer.10.output.dense.weight', 'bert_model.encoder.layer.7.attention.self.query.bias', 'bert_model.encoder.layer.4.attention.self.query.weight', 'bert_model.encoder.layer.4.intermediate.dense.bias', 'bert_model.encoder.layer.11.attention.self.key.weight', 'bert_model.encoder.layer.2.attention.self.query.bias', 'bert_model.encoder.layer.5.attention.output.dense.bias', 'bert_model.encoder.layer.2.attention.output.dense.weight', 'bert_model.encoder.layer.3.output.LayerNorm.weight', 'bert_model.encoder.layer.6.attention.output.dense.weight', 'bert_model.encoder.layer.8.attention.self.key.bias', 'bert_model.embeddings.LayerNorm.bias', 'bert_model.encoder.layer.2.intermediate.dense.weight', 'bert_model.encoder.layer.10.output.LayerNorm.bias', 'bert_model.encoder.layer.11.attention.output.dense.bias', 'bert_model.encoder.layer.9.attention.self.query.bias', 'bert_model.encoder.layer.1.attention.output.dense.bias', 'bert_model.encoder.layer.8.output.dense.weight', 'bert_model.encoder.layer.10.output.LayerNorm.weight', 'bert_model.encoder.layer.10.attention.output.LayerNorm.bias', 'bert_model.encoder.layer.7.intermediate.dense.bias', 'bert_model.encoder.layer.2.attention.self.query.weight', 'bert_model.encoder.layer.8.output.LayerNorm.bias', 'bert_model.encoder.layer.10.intermediate.dense.weight', 'bert_model.encoder.layer.11.attention.self.query.bias', 'bert_model.embeddings.word_embeddings.weight', 'bert_model.encoder.layer.6.output.LayerNorm.weight', 'bert_model.encoder.layer.3.intermediate.dense.bias', 'bert_model.encoder.layer.9.attention.output.LayerNorm.weight', 'bert_model.encoder.layer.1.output.dense.bias', 'bert_model.encoder.layer.9.output.LayerNorm.bias', 'bert_model.encoder.layer.1.output.LayerNorm.weight', 'bert_model.encoder.layer.1.intermediate.dense.bias', 'bert_model.encoder.layer.7.attention.self.query.weight', 'bert_model.encoder.layer.6.attention.output.LayerNorm.weight', 'bert_model.encoder.layer.8.output.LayerNorm.weight', 'bert_model.encoder.layer.10.attention.self.value.bias', 'bert_model.encoder.layer.0.attention.self.query.weight', 'bert_model.encoder.layer.4.attention.output.dense.weight', 'bert_model.encoder.layer.4.output.dense.weight', 'bert_model.encoder.layer.3.intermediate.dense.weight', 'bert_model.encoder.layer.7.output.dense.bias', 'bert_model.encoder.layer.7.output.LayerNorm.bias', 'bert_model.encoder.layer.1.attention.output.dense.weight', 'bert_model.encoder.layer.7.attention.output.LayerNorm.bias', 'bert_model.encoder.layer.6.attention.output.LayerNorm.bias', 'bert_model.encoder.layer.11.output.dense.bias', 'bert_model.encoder.layer.6.output.LayerNorm.bias', 'bert_model.encoder.layer.2.output.dense.weight', 'bert_model.encoder.layer.3.attention.output.LayerNorm.bias', 'bert_model.encoder.layer.11.attention.output.dense.weight', 'bert_model.encoder.layer.9.intermediate.dense.bias', 'bert_model.encoder.layer.4.attention.self.key.bias', 'bert_model.encoder.layer.0.attention.output.dense.bias', 'bert_model.encoder.layer.6.attention.self.query.bias', 'bert_model.encoder.layer.5.output.LayerNorm.weight', 'bert_model.encoder.layer.5.attention.output.dense.weight', 'bert_model.encoder.layer.9.output.dense.bias', 'bert_model.encoder.layer.11.attention.output.LayerNorm.weight', 'bert_model.encoder.layer.10.attention.self.query.bias', 'bert_model.encoder.layer.1.attention.self.query.weight', 'bert_model.encoder.layer.3.attention.self.key.bias', 'bert_model.encoder.layer.4.attention.output.LayerNorm.bias', 'bert_model.encoder.layer.5.intermediate.dense.weight', 'bert_model.encoder.layer.8.intermediate.dense.weight', 'bert_model.encoder.layer.5.attention.self.value.bias', 'bert_model.encoder.layer.11.attention.self.query.weight', 'bert_model.encoder.layer.11.intermediate.dense.weight', 'bert_model.encoder.layer.3.attention.self.query.weight', 'bert_model.encoder.layer.5.attention.self.query.bias', 'bert_model.encoder.layer.8.attention.self.query.bias', 'bert_model.encoder.layer.11.output.dense.weight', 'bert_model.encoder.layer.3.output.dense.bias', 'bert_model.encoder.layer.5.attention.self.value.weight', 'bert_model.encoder.layer.10.attention.output.dense.bias', 'bert_model.encoder.layer.2.attention.self.value.bias', 'bert_model.encoder.layer.3.attention.output.dense.weight', 'bert_model.embeddings.position_embeddings.weight', 'bert_model.encoder.layer.3.output.dense.weight', 'bert_model.encoder.layer.7.attention.output.dense.bias', 'bert_model.encoder.layer.0.attention.output.dense.weight', 'bert_model.encoder.layer.8.attention.output.LayerNorm.bias', 'bert_model.encoder.layer.9.intermediate.dense.weight', 'bert_model.embeddings.LayerNorm.weight', 'bert_model.encoder.layer.1.attention.self.value.bias', 'bert_model.encoder.layer.3.attention.self.value.weight', 'bert_model.encoder.layer.6.attention.self.key.bias', 'bert_model.encoder.layer.11.output.LayerNorm.weight', 'bert_model.encoder.layer.8.attention.output.dense.weight', 'bert_model.encoder.layer.9.attention.self.query.weight', 'bert_model.encoder.layer.7.attention.self.key.weight', 'bert_model.encoder.layer.4.output.dense.bias', 'bert_model.encoder.layer.9.attention.self.key.weight', 'bert_model.encoder.layer.6.intermediate.dense.weight', 'bert_model.encoder.layer.9.attention.output.dense.weight', 'bert_model.encoder.layer.6.output.dense.bias']\n", "You should probably TRAIN this model on a down-stream task to be able to use it for predictions and inference.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["🔧 分词器的作用：文本 → 数字\n", "1. 分词结果: ['my', 'name', 'is', 'le', '##bron', 'james']\n", "2. Token IDs: [2026, 2171, 2003, 3393, 21337, 2508]\n", "3. 最终输入张量:\n", "   input_ids shape: torch.Size([1, 8])\n", "   input_ids: tensor([[  101,  2026,  2171,  2003,  3393, 21337,  2508,   102]])\n", "   attention_mask: tensor([[1, 1, 1, 1, 1, 1, 1, 1]])\n", "\n", "📝 分词器总结:\n", "- 输入: 原始文本字符串\n", "- 输出: 数字张量 (token IDs)\n", "- 作用: 将人类语言转换为模型能理解的数字\n"]}], "source": ["# 直接从dir目录加载（不需要子目录）\n", "model_path = \"/Users/<USER>/Documents/Code/Experiment/robust-tableqa-main/dir\"\n", "\n", "# 加载分词器和模型\n", "tokenizer = DPRQuestionEncoderTokenizer.from_pretrained(model_path)\n", "model = DPRQuestionEncoder.from_pretrained(model_path)\n", "\n", "print(\"🔧 分词器的作用：文本 → 数字\")\n", "# 步骤1: 分词的作用就是将文本转换为数字，只是将其单纯的将文本转换为数字，并没有学习到语义  \n", "tokens = tokenizer.tokenize(text)\n", "print(f\"1. 分词结果: {tokens}\")\n", "\n", "# 步骤2: 转换为ID\n", "token_ids = tokenizer.convert_tokens_to_ids(tokens)\n", "print(f\"2. Token IDs: {token_ids}\")\n", "\n", "# 步骤3: 添加特殊token并padding\n", "encoded = tokenizer(\n", "    text,\n", "    padding=True,\n", "    truncation=True,\n", "    max_length=512,\n", "    return_tensors=\"pt\"\n", ")\n", "\n", "print(f\"3. 最终输入张量:\")\n", "print(f\"   input_ids shape: {encoded['input_ids'].shape}\")\n", "print(f\"   input_ids: {encoded['input_ids']}\")\n", "print(f\"   attention_mask: {encoded['attention_mask']}\")\n", "\n", "print(\"\\n📝 分词器总结:\")\n", "print(\"- 输入: 原始文本字符串\")\n", "print(\"- 输出: 数字张量 (token IDs)\")\n", "print(\"- 作用: 将人类语言转换为模型能理解的数字\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. 编码器 (Encoder) - 语义理解器"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["You are using a model of type bert to instantiate a model of type dpr. This is not supported for all configurations of models and can yield errors.\n", "Some weights of the model checkpoint at /Users/<USER>/Documents/Code/Experiment/robust-tableqa-main/dir were not used when initializing DPRQuestionEncoder: ['pooler.dense.bias', 'encoder.layer.4.attention.self.key.bias', 'encoder.layer.11.attention.output.dense.bias', 'encoder.layer.8.output.dense.weight', 'encoder.layer.3.attention.self.key.weight', 'embeddings.LayerNorm.weight', 'encoder.layer.7.attention.output.LayerNorm.bias', 'encoder.layer.8.attention.output.LayerNorm.bias', 'encoder.layer.8.output.dense.bias', 'encoder.layer.9.output.LayerNorm.bias', 'encoder.layer.11.attention.self.value.weight', 'encoder.layer.5.output.LayerNorm.bias', 'encoder.layer.0.attention.output.dense.bias', 'encoder.layer.10.attention.self.value.weight', 'encoder.layer.11.output.LayerNorm.weight', 'encoder.layer.2.attention.output.LayerNorm.bias', 'encoder.layer.1.attention.output.LayerNorm.bias', 'embeddings.token_type_embeddings.weight', 'embeddings.position_embeddings.weight', 'encoder.layer.3.attention.output.dense.weight', 'encoder.layer.9.attention.self.key.weight', 'encoder.layer.3.attention.output.dense.bias', 'encoder.layer.8.attention.self.value.weight', 'embeddings.word_embeddings.weight', 'encoder.layer.9.output.dense.bias', 'encoder.layer.6.attention.self.query.weight', 'encoder.layer.10.attention.self.key.weight', 'encoder.layer.9.attention.self.query.weight', 'encoder.layer.9.intermediate.dense.bias', 'encoder.layer.11.intermediate.dense.bias', 'encoder.layer.10.output.LayerNorm.weight', 'encoder.layer.2.attention.self.key.weight', 'encoder.layer.0.attention.self.value.bias', 'encoder.layer.10.attention.output.LayerNorm.bias', 'encoder.layer.10.intermediate.dense.weight', 'encoder.layer.7.intermediate.dense.weight', 'encoder.layer.7.attention.self.value.weight', 'encoder.layer.1.output.dense.weight', 'encoder.layer.2.output.dense.weight', 'encoder.layer.10.output.LayerNorm.bias', 'encoder.layer.11.attention.output.LayerNorm.bias', 'encoder.layer.10.attention.self.value.bias', 'encoder.layer.10.attention.output.dense.bias', 'encoder.layer.3.attention.self.query.weight', 'encoder.layer.9.attention.output.LayerNorm.bias', 'encoder.layer.9.output.LayerNorm.weight', 'encoder.layer.4.attention.output.LayerNorm.weight', 'encoder.layer.2.output.dense.bias', 'encoder.layer.1.attention.self.key.bias', 'encoder.layer.7.intermediate.dense.bias', 'encoder.layer.7.attention.self.key.bias', 'encoder.layer.6.output.dense.bias', 'encoder.layer.10.attention.self.key.bias', 'encoder.layer.7.output.dense.bias', 'encoder.layer.7.output.LayerNorm.weight', 'encoder.layer.5.attention.output.dense.bias', 'encoder.layer.6.output.dense.weight', 'encoder.layer.9.attention.output.dense.weight', 'encoder.layer.9.output.dense.weight', 'encoder.layer.1.attention.self.query.weight', 'encoder.layer.11.output.dense.bias', 'encoder.layer.3.attention.output.LayerNorm.weight', 'encoder.layer.11.attention.self.key.weight', 'encoder.layer.6.attention.self.key.weight', 'encoder.layer.8.attention.self.query.weight', 'encoder.layer.8.output.LayerNorm.weight', 'encoder.layer.7.attention.output.dense.weight', 'encoder.layer.11.attention.self.query.bias', 'encoder.layer.6.attention.output.dense.weight', 'encoder.layer.2.attention.self.query.weight', 'encoder.layer.2.attention.output.dense.weight', 'encoder.layer.9.attention.self.value.weight', 'encoder.layer.1.attention.self.value.weight', 'encoder.layer.4.attention.output.LayerNorm.bias', 'encoder.layer.8.attention.output.LayerNorm.weight', 'pooler.dense.weight', 'encoder.layer.0.output.dense.bias', 'encoder.layer.1.attention.self.query.bias', 'encoder.layer.3.intermediate.dense.weight', 'encoder.layer.3.attention.self.value.bias', 'encoder.layer.3.output.LayerNorm.bias', 'encoder.layer.10.attention.output.dense.weight', 'encoder.layer.5.output.dense.bias', 'encoder.layer.11.intermediate.dense.weight', 'encoder.layer.11.attention.self.key.bias', 'encoder.layer.0.attention.output.LayerNorm.weight', 'encoder.layer.2.intermediate.dense.weight', 'encoder.layer.8.attention.self.value.bias', 'encoder.layer.2.attention.self.value.weight', 'encoder.layer.0.output.dense.weight', 'encoder.layer.5.output.dense.weight', 'encoder.layer.7.output.LayerNorm.bias', 'encoder.layer.1.output.dense.bias', 'encoder.layer.0.output.LayerNorm.weight', 'encoder.layer.3.intermediate.dense.bias', 'encoder.layer.5.intermediate.dense.weight', 'encoder.layer.4.attention.self.query.bias', 'encoder.layer.8.attention.self.key.bias', 'encoder.layer.3.attention.self.query.bias', 'encoder.layer.9.attention.self.query.bias', 'encoder.layer.3.output.LayerNorm.weight', 'encoder.layer.5.output.LayerNorm.weight', 'encoder.layer.5.attention.self.key.bias', 'encoder.layer.4.intermediate.dense.weight', 'encoder.layer.2.output.LayerNorm.weight', 'encoder.layer.3.attention.output.LayerNorm.bias', 'encoder.layer.11.attention.self.query.weight', 'encoder.layer.10.attention.self.query.weight', 'encoder.layer.2.attention.self.key.bias', 'encoder.layer.0.output.LayerNorm.bias', 'encoder.layer.11.output.LayerNorm.bias', 'encoder.layer.11.output.dense.weight', 'encoder.layer.10.output.dense.weight', 'encoder.layer.8.output.LayerNorm.bias', 'encoder.layer.3.output.dense.bias', 'encoder.layer.5.attention.self.value.weight', 'encoder.layer.11.attention.self.value.bias', 'encoder.layer.0.intermediate.dense.weight', 'encoder.layer.5.attention.output.LayerNorm.bias', 'encoder.layer.3.attention.self.value.weight', 'encoder.layer.4.output.LayerNorm.weight', 'encoder.layer.6.output.LayerNorm.weight', 'encoder.layer.7.attention.self.key.weight', 'embeddings.LayerNorm.bias', 'encoder.layer.7.attention.output.dense.bias', 'encoder.layer.1.attention.self.key.weight', 'encoder.layer.1.intermediate.dense.bias', 'encoder.layer.5.attention.self.query.bias', 'encoder.layer.6.output.LayerNorm.bias', 'encoder.layer.4.attention.output.dense.weight', 'encoder.layer.0.attention.self.query.bias', 'encoder.layer.2.output.LayerNorm.bias', 'encoder.layer.6.intermediate.dense.bias', 'encoder.layer.1.attention.output.dense.weight', 'encoder.layer.0.attention.self.key.bias', 'encoder.layer.0.attention.self.key.weight', 'encoder.layer.4.output.LayerNorm.bias', 'encoder.layer.6.attention.self.value.bias', 'encoder.layer.4.intermediate.dense.bias', 'encoder.layer.4.output.dense.bias', 'encoder.layer.8.attention.output.dense.bias', 'encoder.layer.7.attention.self.value.bias', 'encoder.layer.8.attention.self.query.bias', 'encoder.layer.7.attention.self.query.weight', 'encoder.layer.7.attention.self.query.bias', 'encoder.layer.4.attention.self.value.bias', 'encoder.layer.6.attention.self.value.weight', 'encoder.layer.9.attention.self.key.bias', 'encoder.layer.7.output.dense.weight', 'encoder.layer.4.attention.output.dense.bias', 'encoder.layer.6.attention.self.key.bias', 'encoder.layer.9.attention.output.LayerNorm.weight', 'encoder.layer.10.attention.output.LayerNorm.weight', 'encoder.layer.8.attention.output.dense.weight', 'encoder.layer.2.attention.self.value.bias', 'encoder.layer.5.attention.self.query.weight', 'encoder.layer.5.attention.self.value.bias', 'encoder.layer.6.attention.output.dense.bias', 'encoder.layer.5.attention.output.LayerNorm.weight', 'encoder.layer.1.output.LayerNorm.weight', 'encoder.layer.6.attention.self.query.bias', 'encoder.layer.10.intermediate.dense.bias', 'encoder.layer.6.attention.output.LayerNorm.bias', 'encoder.layer.2.attention.self.query.bias', 'encoder.layer.7.attention.output.LayerNorm.weight', 'encoder.layer.1.attention.self.value.bias', 'encoder.layer.8.intermediate.dense.bias', 'encoder.layer.0.attention.output.dense.weight', 'encoder.layer.5.intermediate.dense.bias', 'encoder.layer.2.attention.output.LayerNorm.weight', 'encoder.layer.5.attention.output.dense.weight', 'encoder.layer.10.attention.self.query.bias', 'encoder.layer.5.attention.self.key.weight', 'encoder.layer.8.intermediate.dense.weight', 'encoder.layer.3.attention.self.key.bias', 'encoder.layer.0.intermediate.dense.bias', 'encoder.layer.6.intermediate.dense.weight', 'encoder.layer.10.output.dense.bias', 'encoder.layer.0.attention.self.value.weight', 'encoder.layer.4.attention.self.key.weight', 'encoder.layer.1.output.LayerNorm.bias', 'encoder.layer.2.attention.output.dense.bias', 'encoder.layer.9.attention.self.value.bias', 'encoder.layer.9.intermediate.dense.weight', 'encoder.layer.1.intermediate.dense.weight', 'encoder.layer.9.attention.output.dense.bias', 'encoder.layer.0.attention.output.LayerNorm.bias', 'encoder.layer.4.attention.self.query.weight', 'encoder.layer.0.attention.self.query.weight', 'encoder.layer.1.attention.output.LayerNorm.weight', 'encoder.layer.8.attention.self.key.weight', 'encoder.layer.4.output.dense.weight', 'encoder.layer.3.output.dense.weight', 'encoder.layer.11.attention.output.dense.weight', 'encoder.layer.4.attention.self.value.weight', 'encoder.layer.6.attention.output.LayerNorm.weight', 'embeddings.position_ids', 'encoder.layer.1.attention.output.dense.bias', 'encoder.layer.11.attention.output.LayerNorm.weight', 'encoder.layer.2.intermediate.dense.bias']\n", "- This IS expected if you are initializing DPRQuestionEncoder from the checkpoint of a model trained on another task or with another architecture (e.g. initializing a BertForSequenceClassification model from a BertForPreTraining model).\n", "- This IS NOT expected if you are initializing DPRQuestionEncoder from the checkpoint of a model that you expect to be exactly identical (initializing a BertForSequenceClassification model from a BertForSequenceClassification model).\n", "Some weights of DPRQuestionEncoder were not initialized from the model checkpoint at /Users/<USER>/Documents/Code/Experiment/robust-tableqa-main/dir and are newly initialized: ['bert_model.encoder.layer.2.attention.self.value.weight', 'bert_model.encoder.layer.6.attention.self.value.bias', 'bert_model.encoder.layer.0.attention.output.LayerNorm.weight', 'bert_model.encoder.layer.7.output.dense.weight', 'bert_model.encoder.layer.6.attention.self.query.weight', 'bert_model.encoder.layer.1.attention.output.LayerNorm.weight', 'bert_model.encoder.layer.1.output.dense.weight', 'bert_model.encoder.layer.11.intermediate.dense.bias', 'bert_model.encoder.layer.10.attention.self.value.weight', 'bert_model.encoder.layer.10.attention.self.key.bias', 'bert_model.encoder.layer.10.attention.output.dense.weight', 'bert_model.encoder.layer.11.attention.self.value.weight', 'bert_model.encoder.layer.0.attention.self.value.weight', 'bert_model.encoder.layer.0.attention.output.LayerNorm.bias', 'bert_model.encoder.layer.9.attention.self.value.weight', 'bert_model.encoder.layer.9.attention.output.dense.bias', 'bert_model.encoder.layer.5.attention.self.key.weight', 'bert_model.encoder.layer.3.attention.self.key.weight', 'bert_model.encoder.layer.1.attention.self.value.weight', 'bert_model.encoder.layer.0.output.dense.weight', 'bert_model.encoder.layer.3.attention.output.dense.bias', 'bert_model.encoder.layer.1.attention.self.key.weight', 'bert_model.encoder.layer.5.attention.output.LayerNorm.weight', 'bert_model.encoder.layer.6.attention.self.key.weight', 'bert_model.encoder.layer.9.output.LayerNorm.weight', 'bert_model.encoder.layer.8.output.dense.bias', 'bert_model.encoder.layer.11.attention.self.key.bias', 'bert_model.encoder.layer.4.attention.self.key.weight', 'bert_model.encoder.layer.0.intermediate.dense.bias', 'bert_model.encoder.layer.4.attention.self.value.bias', 'bert_model.embeddings.token_type_embeddings.weight', 'bert_model.encoder.layer.10.intermediate.dense.bias', 'bert_model.encoder.layer.6.attention.output.dense.bias', 'bert_model.encoder.layer.5.attention.self.query.weight', 'bert_model.encoder.layer.8.attention.output.LayerNorm.weight', 'bert_model.encoder.layer.10.attention.output.LayerNorm.weight', 'bert_model.encoder.layer.11.attention.output.LayerNorm.bias', 'bert_model.encoder.layer.2.attention.output.dense.bias', 'bert_model.encoder.layer.10.attention.self.query.weight', 'bert_model.encoder.layer.3.output.LayerNorm.bias', 'bert_model.encoder.layer.8.attention.output.dense.bias', 'bert_model.encoder.layer.0.attention.self.value.bias', 'bert_model.encoder.layer.1.intermediate.dense.weight', 'bert_model.encoder.layer.5.output.LayerNorm.bias', 'bert_model.encoder.layer.0.output.LayerNorm.weight', 'bert_model.encoder.layer.5.output.dense.weight', 'bert_model.encoder.layer.7.attention.output.LayerNorm.weight', 'bert_model.encoder.layer.7.attention.self.value.bias', 'bert_model.encoder.layer.4.output.LayerNorm.bias', 'bert_model.encoder.layer.8.attention.self.query.weight', 'bert_model.encoder.layer.7.attention.self.key.bias', 'bert_model.encoder.layer.3.attention.self.value.bias', 'bert_model.encoder.layer.2.output.dense.bias', 'bert_model.encoder.layer.4.attention.output.dense.bias', 'bert_model.encoder.layer.0.attention.self.query.bias', 'bert_model.encoder.layer.2.output.LayerNorm.weight', 'bert_model.encoder.layer.4.attention.output.LayerNorm.weight', 'bert_model.encoder.layer.10.output.dense.bias', 'bert_model.encoder.layer.4.attention.self.query.bias', 'bert_model.encoder.layer.4.attention.self.value.weight', 'bert_model.encoder.layer.5.attention.self.key.bias', 'bert_model.encoder.layer.7.attention.self.value.weight', 'bert_model.encoder.layer.0.attention.self.key.weight', 'bert_model.encoder.layer.1.attention.output.LayerNorm.bias', 'bert_model.encoder.layer.2.intermediate.dense.bias', 'bert_model.encoder.layer.3.attention.output.LayerNorm.weight', 'bert_model.encoder.layer.2.output.LayerNorm.bias', 'bert_model.encoder.layer.0.attention.self.key.bias', 'bert_model.encoder.layer.9.attention.self.key.bias', 'bert_model.encoder.layer.9.attention.self.value.bias', 'bert_model.encoder.layer.4.intermediate.dense.weight', 'bert_model.encoder.layer.9.attention.output.LayerNorm.bias', 'bert_model.encoder.layer.4.output.LayerNorm.weight', 'bert_model.encoder.layer.6.attention.self.value.weight', 'bert_model.encoder.layer.1.attention.self.key.bias', 'bert_model.encoder.layer.6.output.dense.weight', 'bert_model.encoder.layer.2.attention.output.LayerNorm.bias', 'bert_model.encoder.layer.7.output.LayerNorm.weight', 'bert_model.encoder.layer.3.attention.self.query.bias', 'bert_model.encoder.layer.7.attention.output.dense.weight', 'bert_model.encoder.layer.8.attention.self.value.bias', 'bert_model.encoder.layer.8.intermediate.dense.bias', 'bert_model.encoder.layer.5.output.dense.bias', 'bert_model.encoder.layer.2.attention.self.key.bias', 'bert_model.encoder.layer.10.attention.self.key.weight', 'bert_model.encoder.layer.2.attention.output.LayerNorm.weight', 'bert_model.encoder.layer.8.attention.self.value.weight', 'bert_model.encoder.layer.9.output.dense.weight', 'bert_model.encoder.layer.5.attention.output.LayerNorm.bias', 'bert_model.encoder.layer.0.intermediate.dense.weight', 'bert_model.encoder.layer.6.intermediate.dense.bias', 'bert_model.encoder.layer.11.output.LayerNorm.bias', 'bert_model.encoder.layer.0.output.dense.bias', 'bert_model.encoder.layer.8.attention.self.key.weight', 'bert_model.encoder.layer.1.output.LayerNorm.bias', 'bert_model.encoder.layer.11.attention.self.value.bias', 'bert_model.encoder.layer.0.output.LayerNorm.bias', 'bert_model.encoder.layer.1.attention.self.query.bias', 'bert_model.encoder.layer.5.intermediate.dense.bias', 'bert_model.encoder.layer.2.attention.self.key.weight', 'bert_model.encoder.layer.7.intermediate.dense.weight', 'bert_model.encoder.layer.10.output.dense.weight', 'bert_model.encoder.layer.7.attention.self.query.bias', 'bert_model.encoder.layer.4.attention.self.query.weight', 'bert_model.encoder.layer.4.intermediate.dense.bias', 'bert_model.encoder.layer.11.attention.self.key.weight', 'bert_model.encoder.layer.2.attention.self.query.bias', 'bert_model.encoder.layer.5.attention.output.dense.bias', 'bert_model.encoder.layer.2.attention.output.dense.weight', 'bert_model.encoder.layer.3.output.LayerNorm.weight', 'bert_model.encoder.layer.6.attention.output.dense.weight', 'bert_model.encoder.layer.8.attention.self.key.bias', 'bert_model.embeddings.LayerNorm.bias', 'bert_model.encoder.layer.2.intermediate.dense.weight', 'bert_model.encoder.layer.10.output.LayerNorm.bias', 'bert_model.encoder.layer.11.attention.output.dense.bias', 'bert_model.encoder.layer.9.attention.self.query.bias', 'bert_model.encoder.layer.1.attention.output.dense.bias', 'bert_model.encoder.layer.8.output.dense.weight', 'bert_model.encoder.layer.10.output.LayerNorm.weight', 'bert_model.encoder.layer.10.attention.output.LayerNorm.bias', 'bert_model.encoder.layer.7.intermediate.dense.bias', 'bert_model.encoder.layer.2.attention.self.query.weight', 'bert_model.encoder.layer.8.output.LayerNorm.bias', 'bert_model.encoder.layer.10.intermediate.dense.weight', 'bert_model.encoder.layer.11.attention.self.query.bias', 'bert_model.embeddings.word_embeddings.weight', 'bert_model.encoder.layer.6.output.LayerNorm.weight', 'bert_model.encoder.layer.3.intermediate.dense.bias', 'bert_model.encoder.layer.9.attention.output.LayerNorm.weight', 'bert_model.encoder.layer.1.output.dense.bias', 'bert_model.encoder.layer.9.output.LayerNorm.bias', 'bert_model.encoder.layer.1.output.LayerNorm.weight', 'bert_model.encoder.layer.1.intermediate.dense.bias', 'bert_model.encoder.layer.7.attention.self.query.weight', 'bert_model.encoder.layer.6.attention.output.LayerNorm.weight', 'bert_model.encoder.layer.8.output.LayerNorm.weight', 'bert_model.encoder.layer.10.attention.self.value.bias', 'bert_model.encoder.layer.0.attention.self.query.weight', 'bert_model.encoder.layer.4.attention.output.dense.weight', 'bert_model.encoder.layer.4.output.dense.weight', 'bert_model.encoder.layer.3.intermediate.dense.weight', 'bert_model.encoder.layer.7.output.dense.bias', 'bert_model.encoder.layer.7.output.LayerNorm.bias', 'bert_model.encoder.layer.1.attention.output.dense.weight', 'bert_model.encoder.layer.7.attention.output.LayerNorm.bias', 'bert_model.encoder.layer.6.attention.output.LayerNorm.bias', 'bert_model.encoder.layer.11.output.dense.bias', 'bert_model.encoder.layer.6.output.LayerNorm.bias', 'bert_model.encoder.layer.2.output.dense.weight', 'bert_model.encoder.layer.3.attention.output.LayerNorm.bias', 'bert_model.encoder.layer.11.attention.output.dense.weight', 'bert_model.encoder.layer.9.intermediate.dense.bias', 'bert_model.encoder.layer.4.attention.self.key.bias', 'bert_model.encoder.layer.0.attention.output.dense.bias', 'bert_model.encoder.layer.6.attention.self.query.bias', 'bert_model.encoder.layer.5.output.LayerNorm.weight', 'bert_model.encoder.layer.5.attention.output.dense.weight', 'bert_model.encoder.layer.9.output.dense.bias', 'bert_model.encoder.layer.11.attention.output.LayerNorm.weight', 'bert_model.encoder.layer.10.attention.self.query.bias', 'bert_model.encoder.layer.1.attention.self.query.weight', 'bert_model.encoder.layer.3.attention.self.key.bias', 'bert_model.encoder.layer.4.attention.output.LayerNorm.bias', 'bert_model.encoder.layer.5.intermediate.dense.weight', 'bert_model.encoder.layer.8.intermediate.dense.weight', 'bert_model.encoder.layer.5.attention.self.value.bias', 'bert_model.encoder.layer.11.attention.self.query.weight', 'bert_model.encoder.layer.11.intermediate.dense.weight', 'bert_model.encoder.layer.3.attention.self.query.weight', 'bert_model.encoder.layer.5.attention.self.query.bias', 'bert_model.encoder.layer.8.attention.self.query.bias', 'bert_model.encoder.layer.11.output.dense.weight', 'bert_model.encoder.layer.3.output.dense.bias', 'bert_model.encoder.layer.5.attention.self.value.weight', 'bert_model.encoder.layer.10.attention.output.dense.bias', 'bert_model.encoder.layer.2.attention.self.value.bias', 'bert_model.encoder.layer.3.attention.output.dense.weight', 'bert_model.embeddings.position_embeddings.weight', 'bert_model.encoder.layer.3.output.dense.weight', 'bert_model.encoder.layer.7.attention.output.dense.bias', 'bert_model.encoder.layer.0.attention.output.dense.weight', 'bert_model.encoder.layer.8.attention.output.LayerNorm.bias', 'bert_model.encoder.layer.9.intermediate.dense.weight', 'bert_model.embeddings.LayerNorm.weight', 'bert_model.encoder.layer.1.attention.self.value.bias', 'bert_model.encoder.layer.3.attention.self.value.weight', 'bert_model.encoder.layer.6.attention.self.key.bias', 'bert_model.encoder.layer.11.output.LayerNorm.weight', 'bert_model.encoder.layer.8.attention.output.dense.weight', 'bert_model.encoder.layer.9.attention.self.query.weight', 'bert_model.encoder.layer.7.attention.self.key.weight', 'bert_model.encoder.layer.4.output.dense.bias', 'bert_model.encoder.layer.9.attention.self.key.weight', 'bert_model.encoder.layer.6.intermediate.dense.weight', 'bert_model.encoder.layer.9.attention.output.dense.weight', 'bert_model.encoder.layer.6.output.dense.bias']\n", "You should probably TRAIN this model on a down-stream task to be able to use it for predictions and inference.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["🧠 编码器的作用：数字 → 语义向量\n", "\n", "编码器输入: torch.<PERSON><PERSON>([1, 8]) (来自分词器)\n", "编码器输出: torch.<PERSON><PERSON>([1, 768]) (语义向量)\n", "向量前5个值: [-0.23337449  0.505107   -0.05914158 -1.3500632   0.9138332 ]\n", "\n", "🧠 编码器总结:\n", "- 输入: 数字张量 (来自分词器)\n", "- 输出: 高维语义向量 (768维)\n", "- 作用: 理解文本语义，生成可比较的向量表示\n"]}], "source": ["model_path = \"/Users/<USER>/Documents/Code/Experiment/robust-tableqa-main/dir\"\n", "# 加载编码器，又称之为语义理解器。\n", "encoder = DPRQuestionEncoder.from_pretrained(\n", "    model_path\n", ")\n", "\n", "print(\"🧠 编码器的作用：数字 → 语义向量\")\n", "print()\n", "\n", "# 使用编码器处理分词器的输出\n", "with torch.no_grad():\n", "    outputs = encoder(**encoded)\n", "    embeddings = outputs.pooler_output\n", "\n", "print(f\"编码器输入: {encoded['input_ids'].shape} (来自分词器)\")\n", "print(f\"编码器输出: {embeddings.shape} (语义向量)\")\n", "print(f\"向量前5个值: {embeddings[0][:5].numpy()}\")\n", "\n", "print(\"\\n🧠 编码器总结:\")\n", "print(\"- 输入: 数字张量 (来自分词器)\")\n", "print(\"- 输出: 高维语义向量 (768维)\")\n", "print(\"- 作用: 理解文本语义，生成可比较的向量表示\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. 完整流水线对比"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔄 处理流水线演示\n", "文本1: '什么是深度学习？'\n", "文本2: '深度学习是什么？'\n", "\n", "步骤1: 分词器处理\n", "  文本1 → token_ids: tensor([ 101,  100,  100,  100,  100,  100, 1817,  100, 1994,  102])...\n", "  文本2 → token_ids: tensor([ 101,  100,  100, 1817,  100,  100,  100,  100, 1994,  102])...\n", "\n", "步骤2: 编码器处理\n", "  文本1 → 向量: shape=torch.<PERSON><PERSON>([1, 768]), 前3值=[-0.6043679  -0.04743408  0.13103193]\n", "  文本2 → 向量: shape=torch.<PERSON><PERSON>([1, 768]), 前3值=[-0.62380314 -0.033572    0.04982982]\n", "\n", "步骤3: 语义相似度计算\n", "  相似度: 0.9995\n", "\n", "==================================================\n", "🔄 处理流水线演示\n", "文本1: '什么是深度学习？'\n", "文本2: '今天天气很好'\n", "\n", "步骤1: 分词器处理\n", "  文本1 → token_ids: tensor([ 101,  100,  100,  100,  100,  100, 1817,  100, 1994,  102])...\n", "  文本2 → token_ids: tensor([ 101,  100, 1811, 1811,  100,  100,  100,  102])...\n", "\n", "步骤2: 编码器处理\n", "  文本1 → 向量: shape=torch.<PERSON><PERSON>([1, 768]), 前3值=[-0.6043679  -0.04743408  0.13103193]\n", "  文本2 → 向量: shape=torch.<PERSON><PERSON>([1, 768]), 前3值=[-0.27332908  0.04514417  0.07813466]\n", "\n", "步骤3: 语义相似度计算\n", "  相似度: 0.9829\n", "\n", "📊 结果分析:\n", "相似文本相似度: 0.9995 (高)\n", "不同文本相似度: 0.9829 (低)\n", "这证明编码器能理解语义！\n"]}], "source": ["def demonstrate_pipeline(text1, text2):\n", "    \"\"\"演示完整的处理流水线\"\"\"\n", "    print(f\"🔄 处理流水线演示\")\n", "    print(f\"文本1: '{text1}'\")\n", "    print(f\"文本2: '{text2}'\")\n", "    print()\n", "    \n", "    # 步骤1: 分词器处理\n", "    print(\"步骤1: 分词器处理\")\n", "    inputs1 = tokenizer(text1, return_tensors=\"pt\", padding=True, truncation=True)\n", "    inputs2 = tokenizer(text2, return_tensors=\"pt\", padding=True, truncation=True)\n", "    \n", "    print(f\"  文本1 → token_ids: {inputs1['input_ids'][0][:10]}...\")\n", "    print(f\"  文本2 → token_ids: {inputs2['input_ids'][0][:10]}...\")\n", "    \n", "    # 步骤2: 编码器处理\n", "    print(\"\\n步骤2: 编码器处理\")\n", "    with torch.no_grad():\n", "        emb1 = encoder(**inputs1).pooler_output\n", "        emb2 = encoder(**inputs2).pooler_output\n", "    \n", "    print(f\"  文本1 → 向量: shape={emb1.shape}, 前3值={emb1[0][:3].numpy()}\")\n", "    print(f\"  文本2 → 向量: shape={emb2.shape}, 前3值={emb2[0][:3].numpy()}\")\n", "    \n", "    # 步骤3: 语义相似度计算\n", "    print(\"\\n步骤3: 语义相似度计算\")\n", "    similarity = torch.cosine_similarity(emb1, emb2)\n", "    print(f\"  相似度: {similarity.item():.4f}\")\n", "    \n", "    return similarity.item()\n", "\n", "# 测试相似文本\n", "sim1 = demonstrate_pipeline(\n", "    \"什么是深度学习？\", \n", "    \"深度学习是什么？\"\n", ")\n", "\n", "print(\"\\n\" + \"=\"*50)\n", "\n", "# 测试不相似文本\n", "sim2 = demonstrate_pipeline(\n", "    \"什么是深度学习？\", \n", "    \"今天天气很好\"\n", ")\n", "\n", "print(f\"\\n📊 结果分析:\")\n", "print(f\"相似文本相似度: {sim1:.4f} (高)\")\n", "print(f\"不同文本相似度: {sim2:.4f} (低)\")\n", "print(\"这证明编码器能理解语义！\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. 关键区别总结"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🎯 分词器 vs 编码器 - 关键区别\n", "============================================================\n", "输入           | 文本字符串                | 整数ID序列\n", "输出           | 整数ID序列               | 语义向量\n", "维度           | [seq_len]            | [batch, 768]\n", "作用           | 文本预处理                | 语义理解\n", "可训练          | 否 (规则/词表)            | 是 (神经网络)\n", "计算复杂度        | 低 (查表操作)             | 高 (深度网络)\n", "\n", "🔗 它们的关系:\n", "文本 → [分词器] → Token IDs → [编码器] → 语义向量\n", "     (预处理)              (理解语义)\n", "\n", "💡 类比理解:\n", "分词器 = 翻译官 (将中文翻译成数字)\n", "编码器 = 理解者 (理解数字背后的含义)\n"]}], "source": ["print(\"🎯 分词器 vs 编码器 - 关键区别\")\n", "print(\"=\" * 60)\n", "\n", "comparison = {\n", "    \"特征\": [\"输入\", \"输出\", \"维度\", \"作用\", \"可训练\", \"计算复杂度\"],\n", "    \"分词器 (Tokenizer)\": [\n", "        \"文本字符串\",\n", "        \"整数ID序列\", \n", "        \"[seq_len]\",\n", "        \"文本预处理\",\n", "        \"否 (规则/词表)\",\n", "        \"低 (查表操作)\"\n", "    ],\n", "    \"编码器 (Encoder)\": [\n", "        \"整数ID序列\",\n", "        \"语义向量\",\n", "        \"[batch, 768]\", \n", "        \"语义理解\",\n", "        \"是 (神经网络)\",\n", "        \"高 (深度网络)\"\n", "    ]\n", "}\n", "\n", "for i, feature in enumerate(comparison[\"特征\"]):\n", "    print(f\"{feature:12} | {comparison['分词器 (Tokenizer)'][i]:20} | {comparison['编码器 (Encoder)'][i]}\")\n", "\n", "print(\"\\n🔗 它们的关系:\")\n", "print(\"文本 → [分词器] → Token IDs → [编码器] → 语义向量\")\n", "print(\"     (预处理)              (理解语义)\")\n", "\n", "print(\"\\n💡 类比理解:\")\n", "print(\"分词器 = 翻译官 (将中文翻译成数字)\")\n", "print(\"编码器 = 理解者 (理解数字背后的含义)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. 实际应用中的配合"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["The tokenizer class you load from this checkpoint is not the same type as the class this function is called from. It may result in unexpected tokenization. \n", "The tokenizer class you load from this checkpoint is 'BertTokenizer'. \n", "The class this function is called from is 'DPRQuestionEncoderTokenizer'.\n", "You are using a model of type bert to instantiate a model of type dpr. This is not supported for all configurations of models and can yield errors.\n", "/Volumes/Data/miniconda/miniconda3/envs/tableqa/lib/python3.8/site-packages/transformers/modeling_utils.py:367: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.\n", "  return torch.load(checkpoint_file, map_location=\"cpu\")\n", "Some weights of the model checkpoint at /Users/<USER>/Documents/Code/Experiment/robust-tableqa-main/dir were not used when initializing DPRQuestionEncoder: ['pooler.dense.bias', 'encoder.layer.4.attention.self.key.bias', 'encoder.layer.11.attention.output.dense.bias', 'encoder.layer.8.output.dense.weight', 'encoder.layer.3.attention.self.key.weight', 'embeddings.LayerNorm.weight', 'encoder.layer.7.attention.output.LayerNorm.bias', 'encoder.layer.8.attention.output.LayerNorm.bias', 'encoder.layer.8.output.dense.bias', 'encoder.layer.9.output.LayerNorm.bias', 'encoder.layer.11.attention.self.value.weight', 'encoder.layer.5.output.LayerNorm.bias', 'encoder.layer.0.attention.output.dense.bias', 'encoder.layer.10.attention.self.value.weight', 'encoder.layer.11.output.LayerNorm.weight', 'encoder.layer.2.attention.output.LayerNorm.bias', 'encoder.layer.1.attention.output.LayerNorm.bias', 'embeddings.token_type_embeddings.weight', 'embeddings.position_embeddings.weight', 'encoder.layer.3.attention.output.dense.weight', 'encoder.layer.9.attention.self.key.weight', 'encoder.layer.3.attention.output.dense.bias', 'encoder.layer.8.attention.self.value.weight', 'embeddings.word_embeddings.weight', 'encoder.layer.9.output.dense.bias', 'encoder.layer.6.attention.self.query.weight', 'encoder.layer.10.attention.self.key.weight', 'encoder.layer.9.attention.self.query.weight', 'encoder.layer.9.intermediate.dense.bias', 'encoder.layer.11.intermediate.dense.bias', 'encoder.layer.10.output.LayerNorm.weight', 'encoder.layer.2.attention.self.key.weight', 'encoder.layer.0.attention.self.value.bias', 'encoder.layer.10.attention.output.LayerNorm.bias', 'encoder.layer.10.intermediate.dense.weight', 'encoder.layer.7.intermediate.dense.weight', 'encoder.layer.7.attention.self.value.weight', 'encoder.layer.1.output.dense.weight', 'encoder.layer.2.output.dense.weight', 'encoder.layer.10.output.LayerNorm.bias', 'encoder.layer.11.attention.output.LayerNorm.bias', 'encoder.layer.10.attention.self.value.bias', 'encoder.layer.10.attention.output.dense.bias', 'encoder.layer.3.attention.self.query.weight', 'encoder.layer.9.attention.output.LayerNorm.bias', 'encoder.layer.9.output.LayerNorm.weight', 'encoder.layer.4.attention.output.LayerNorm.weight', 'encoder.layer.2.output.dense.bias', 'encoder.layer.1.attention.self.key.bias', 'encoder.layer.7.intermediate.dense.bias', 'encoder.layer.7.attention.self.key.bias', 'encoder.layer.6.output.dense.bias', 'encoder.layer.10.attention.self.key.bias', 'encoder.layer.7.output.dense.bias', 'encoder.layer.7.output.LayerNorm.weight', 'encoder.layer.5.attention.output.dense.bias', 'encoder.layer.6.output.dense.weight', 'encoder.layer.9.attention.output.dense.weight', 'encoder.layer.9.output.dense.weight', 'encoder.layer.1.attention.self.query.weight', 'encoder.layer.11.output.dense.bias', 'encoder.layer.3.attention.output.LayerNorm.weight', 'encoder.layer.11.attention.self.key.weight', 'encoder.layer.6.attention.self.key.weight', 'encoder.layer.8.attention.self.query.weight', 'encoder.layer.8.output.LayerNorm.weight', 'encoder.layer.7.attention.output.dense.weight', 'encoder.layer.11.attention.self.query.bias', 'encoder.layer.6.attention.output.dense.weight', 'encoder.layer.2.attention.self.query.weight', 'encoder.layer.2.attention.output.dense.weight', 'encoder.layer.9.attention.self.value.weight', 'encoder.layer.1.attention.self.value.weight', 'encoder.layer.4.attention.output.LayerNorm.bias', 'encoder.layer.8.attention.output.LayerNorm.weight', 'pooler.dense.weight', 'encoder.layer.0.output.dense.bias', 'encoder.layer.1.attention.self.query.bias', 'encoder.layer.3.intermediate.dense.weight', 'encoder.layer.3.attention.self.value.bias', 'encoder.layer.3.output.LayerNorm.bias', 'encoder.layer.10.attention.output.dense.weight', 'encoder.layer.5.output.dense.bias', 'encoder.layer.11.intermediate.dense.weight', 'encoder.layer.11.attention.self.key.bias', 'encoder.layer.0.attention.output.LayerNorm.weight', 'encoder.layer.2.intermediate.dense.weight', 'encoder.layer.8.attention.self.value.bias', 'encoder.layer.2.attention.self.value.weight', 'encoder.layer.0.output.dense.weight', 'encoder.layer.5.output.dense.weight', 'encoder.layer.7.output.LayerNorm.bias', 'encoder.layer.1.output.dense.bias', 'encoder.layer.0.output.LayerNorm.weight', 'encoder.layer.3.intermediate.dense.bias', 'encoder.layer.5.intermediate.dense.weight', 'encoder.layer.4.attention.self.query.bias', 'encoder.layer.8.attention.self.key.bias', 'encoder.layer.3.attention.self.query.bias', 'encoder.layer.9.attention.self.query.bias', 'encoder.layer.3.output.LayerNorm.weight', 'encoder.layer.5.output.LayerNorm.weight', 'encoder.layer.5.attention.self.key.bias', 'encoder.layer.4.intermediate.dense.weight', 'encoder.layer.2.output.LayerNorm.weight', 'encoder.layer.3.attention.output.LayerNorm.bias', 'encoder.layer.11.attention.self.query.weight', 'encoder.layer.10.attention.self.query.weight', 'encoder.layer.2.attention.self.key.bias', 'encoder.layer.0.output.LayerNorm.bias', 'encoder.layer.11.output.LayerNorm.bias', 'encoder.layer.11.output.dense.weight', 'encoder.layer.10.output.dense.weight', 'encoder.layer.8.output.LayerNorm.bias', 'encoder.layer.3.output.dense.bias', 'encoder.layer.5.attention.self.value.weight', 'encoder.layer.11.attention.self.value.bias', 'encoder.layer.0.intermediate.dense.weight', 'encoder.layer.5.attention.output.LayerNorm.bias', 'encoder.layer.3.attention.self.value.weight', 'encoder.layer.4.output.LayerNorm.weight', 'encoder.layer.6.output.LayerNorm.weight', 'encoder.layer.7.attention.self.key.weight', 'embeddings.LayerNorm.bias', 'encoder.layer.7.attention.output.dense.bias', 'encoder.layer.1.attention.self.key.weight', 'encoder.layer.1.intermediate.dense.bias', 'encoder.layer.5.attention.self.query.bias', 'encoder.layer.6.output.LayerNorm.bias', 'encoder.layer.4.attention.output.dense.weight', 'encoder.layer.0.attention.self.query.bias', 'encoder.layer.2.output.LayerNorm.bias', 'encoder.layer.6.intermediate.dense.bias', 'encoder.layer.1.attention.output.dense.weight', 'encoder.layer.0.attention.self.key.bias', 'encoder.layer.0.attention.self.key.weight', 'encoder.layer.4.output.LayerNorm.bias', 'encoder.layer.6.attention.self.value.bias', 'encoder.layer.4.intermediate.dense.bias', 'encoder.layer.4.output.dense.bias', 'encoder.layer.8.attention.output.dense.bias', 'encoder.layer.7.attention.self.value.bias', 'encoder.layer.8.attention.self.query.bias', 'encoder.layer.7.attention.self.query.weight', 'encoder.layer.7.attention.self.query.bias', 'encoder.layer.4.attention.self.value.bias', 'encoder.layer.6.attention.self.value.weight', 'encoder.layer.9.attention.self.key.bias', 'encoder.layer.7.output.dense.weight', 'encoder.layer.4.attention.output.dense.bias', 'encoder.layer.6.attention.self.key.bias', 'encoder.layer.9.attention.output.LayerNorm.weight', 'encoder.layer.10.attention.output.LayerNorm.weight', 'encoder.layer.8.attention.output.dense.weight', 'encoder.layer.2.attention.self.value.bias', 'encoder.layer.5.attention.self.query.weight', 'encoder.layer.5.attention.self.value.bias', 'encoder.layer.6.attention.output.dense.bias', 'encoder.layer.5.attention.output.LayerNorm.weight', 'encoder.layer.1.output.LayerNorm.weight', 'encoder.layer.6.attention.self.query.bias', 'encoder.layer.10.intermediate.dense.bias', 'encoder.layer.6.attention.output.LayerNorm.bias', 'encoder.layer.2.attention.self.query.bias', 'encoder.layer.7.attention.output.LayerNorm.weight', 'encoder.layer.1.attention.self.value.bias', 'encoder.layer.8.intermediate.dense.bias', 'encoder.layer.0.attention.output.dense.weight', 'encoder.layer.5.intermediate.dense.bias', 'encoder.layer.2.attention.output.LayerNorm.weight', 'encoder.layer.5.attention.output.dense.weight', 'encoder.layer.10.attention.self.query.bias', 'encoder.layer.5.attention.self.key.weight', 'encoder.layer.8.intermediate.dense.weight', 'encoder.layer.3.attention.self.key.bias', 'encoder.layer.0.intermediate.dense.bias', 'encoder.layer.6.intermediate.dense.weight', 'encoder.layer.10.output.dense.bias', 'encoder.layer.0.attention.self.value.weight', 'encoder.layer.4.attention.self.key.weight', 'encoder.layer.1.output.LayerNorm.bias', 'encoder.layer.2.attention.output.dense.bias', 'encoder.layer.9.attention.self.value.bias', 'encoder.layer.9.intermediate.dense.weight', 'encoder.layer.1.intermediate.dense.weight', 'encoder.layer.9.attention.output.dense.bias', 'encoder.layer.0.attention.output.LayerNorm.bias', 'encoder.layer.4.attention.self.query.weight', 'encoder.layer.0.attention.self.query.weight', 'encoder.layer.1.attention.output.LayerNorm.weight', 'encoder.layer.8.attention.self.key.weight', 'encoder.layer.4.output.dense.weight', 'encoder.layer.3.output.dense.weight', 'encoder.layer.11.attention.output.dense.weight', 'encoder.layer.4.attention.self.value.weight', 'encoder.layer.6.attention.output.LayerNorm.weight', 'embeddings.position_ids', 'encoder.layer.1.attention.output.dense.bias', 'encoder.layer.11.attention.output.LayerNorm.weight', 'encoder.layer.2.intermediate.dense.bias']\n", "- This IS expected if you are initializing DPRQuestionEncoder from the checkpoint of a model trained on another task or with another architecture (e.g. initializing a BertForSequenceClassification model from a BertForPreTraining model).\n", "- This IS NOT expected if you are initializing DPRQuestionEncoder from the checkpoint of a model that you expect to be exactly identical (initializing a BertForSequenceClassification model from a BertForSequenceClassification model).\n", "Some weights of DPRQuestionEncoder were not initialized from the model checkpoint at /Users/<USER>/Documents/Code/Experiment/robust-tableqa-main/dir and are newly initialized: ['bert_model.encoder.layer.2.attention.self.value.weight', 'bert_model.encoder.layer.6.attention.self.value.bias', 'bert_model.encoder.layer.0.attention.output.LayerNorm.weight', 'bert_model.encoder.layer.7.output.dense.weight', 'bert_model.encoder.layer.6.attention.self.query.weight', 'bert_model.encoder.layer.1.attention.output.LayerNorm.weight', 'bert_model.encoder.layer.1.output.dense.weight', 'bert_model.encoder.layer.11.intermediate.dense.bias', 'bert_model.encoder.layer.10.attention.self.value.weight', 'bert_model.encoder.layer.10.attention.self.key.bias', 'bert_model.encoder.layer.10.attention.output.dense.weight', 'bert_model.encoder.layer.11.attention.self.value.weight', 'bert_model.encoder.layer.0.attention.self.value.weight', 'bert_model.encoder.layer.0.attention.output.LayerNorm.bias', 'bert_model.encoder.layer.9.attention.self.value.weight', 'bert_model.encoder.layer.9.attention.output.dense.bias', 'bert_model.encoder.layer.5.attention.self.key.weight', 'bert_model.encoder.layer.3.attention.self.key.weight', 'bert_model.encoder.layer.1.attention.self.value.weight', 'bert_model.encoder.layer.0.output.dense.weight', 'bert_model.encoder.layer.3.attention.output.dense.bias', 'bert_model.encoder.layer.1.attention.self.key.weight', 'bert_model.encoder.layer.5.attention.output.LayerNorm.weight', 'bert_model.encoder.layer.6.attention.self.key.weight', 'bert_model.encoder.layer.9.output.LayerNorm.weight', 'bert_model.encoder.layer.8.output.dense.bias', 'bert_model.encoder.layer.11.attention.self.key.bias', 'bert_model.encoder.layer.4.attention.self.key.weight', 'bert_model.encoder.layer.0.intermediate.dense.bias', 'bert_model.encoder.layer.4.attention.self.value.bias', 'bert_model.embeddings.token_type_embeddings.weight', 'bert_model.encoder.layer.10.intermediate.dense.bias', 'bert_model.encoder.layer.6.attention.output.dense.bias', 'bert_model.encoder.layer.5.attention.self.query.weight', 'bert_model.encoder.layer.8.attention.output.LayerNorm.weight', 'bert_model.encoder.layer.10.attention.output.LayerNorm.weight', 'bert_model.encoder.layer.11.attention.output.LayerNorm.bias', 'bert_model.encoder.layer.2.attention.output.dense.bias', 'bert_model.encoder.layer.10.attention.self.query.weight', 'bert_model.encoder.layer.3.output.LayerNorm.bias', 'bert_model.encoder.layer.8.attention.output.dense.bias', 'bert_model.encoder.layer.0.attention.self.value.bias', 'bert_model.encoder.layer.1.intermediate.dense.weight', 'bert_model.encoder.layer.5.output.LayerNorm.bias', 'bert_model.encoder.layer.0.output.LayerNorm.weight', 'bert_model.encoder.layer.5.output.dense.weight', 'bert_model.encoder.layer.7.attention.output.LayerNorm.weight', 'bert_model.encoder.layer.7.attention.self.value.bias', 'bert_model.encoder.layer.4.output.LayerNorm.bias', 'bert_model.encoder.layer.8.attention.self.query.weight', 'bert_model.encoder.layer.7.attention.self.key.bias', 'bert_model.encoder.layer.3.attention.self.value.bias', 'bert_model.encoder.layer.2.output.dense.bias', 'bert_model.encoder.layer.4.attention.output.dense.bias', 'bert_model.encoder.layer.0.attention.self.query.bias', 'bert_model.encoder.layer.2.output.LayerNorm.weight', 'bert_model.encoder.layer.4.attention.output.LayerNorm.weight', 'bert_model.encoder.layer.10.output.dense.bias', 'bert_model.encoder.layer.4.attention.self.query.bias', 'bert_model.encoder.layer.4.attention.self.value.weight', 'bert_model.encoder.layer.5.attention.self.key.bias', 'bert_model.encoder.layer.7.attention.self.value.weight', 'bert_model.encoder.layer.0.attention.self.key.weight', 'bert_model.encoder.layer.1.attention.output.LayerNorm.bias', 'bert_model.encoder.layer.2.intermediate.dense.bias', 'bert_model.encoder.layer.3.attention.output.LayerNorm.weight', 'bert_model.encoder.layer.2.output.LayerNorm.bias', 'bert_model.encoder.layer.0.attention.self.key.bias', 'bert_model.encoder.layer.9.attention.self.key.bias', 'bert_model.encoder.layer.9.attention.self.value.bias', 'bert_model.encoder.layer.4.intermediate.dense.weight', 'bert_model.encoder.layer.9.attention.output.LayerNorm.bias', 'bert_model.encoder.layer.4.output.LayerNorm.weight', 'bert_model.encoder.layer.6.attention.self.value.weight', 'bert_model.encoder.layer.1.attention.self.key.bias', 'bert_model.encoder.layer.6.output.dense.weight', 'bert_model.encoder.layer.2.attention.output.LayerNorm.bias', 'bert_model.encoder.layer.7.output.LayerNorm.weight', 'bert_model.encoder.layer.3.attention.self.query.bias', 'bert_model.encoder.layer.7.attention.output.dense.weight', 'bert_model.encoder.layer.8.attention.self.value.bias', 'bert_model.encoder.layer.8.intermediate.dense.bias', 'bert_model.encoder.layer.5.output.dense.bias', 'bert_model.encoder.layer.2.attention.self.key.bias', 'bert_model.encoder.layer.10.attention.self.key.weight', 'bert_model.encoder.layer.2.attention.output.LayerNorm.weight', 'bert_model.encoder.layer.8.attention.self.value.weight', 'bert_model.encoder.layer.9.output.dense.weight', 'bert_model.encoder.layer.5.attention.output.LayerNorm.bias', 'bert_model.encoder.layer.0.intermediate.dense.weight', 'bert_model.encoder.layer.6.intermediate.dense.bias', 'bert_model.encoder.layer.11.output.LayerNorm.bias', 'bert_model.encoder.layer.0.output.dense.bias', 'bert_model.encoder.layer.8.attention.self.key.weight', 'bert_model.encoder.layer.1.output.LayerNorm.bias', 'bert_model.encoder.layer.11.attention.self.value.bias', 'bert_model.encoder.layer.0.output.LayerNorm.bias', 'bert_model.encoder.layer.1.attention.self.query.bias', 'bert_model.encoder.layer.5.intermediate.dense.bias', 'bert_model.encoder.layer.2.attention.self.key.weight', 'bert_model.encoder.layer.7.intermediate.dense.weight', 'bert_model.encoder.layer.10.output.dense.weight', 'bert_model.encoder.layer.7.attention.self.query.bias', 'bert_model.encoder.layer.4.attention.self.query.weight', 'bert_model.encoder.layer.4.intermediate.dense.bias', 'bert_model.encoder.layer.11.attention.self.key.weight', 'bert_model.encoder.layer.2.attention.self.query.bias', 'bert_model.encoder.layer.5.attention.output.dense.bias', 'bert_model.encoder.layer.2.attention.output.dense.weight', 'bert_model.encoder.layer.3.output.LayerNorm.weight', 'bert_model.encoder.layer.6.attention.output.dense.weight', 'bert_model.encoder.layer.8.attention.self.key.bias', 'bert_model.embeddings.LayerNorm.bias', 'bert_model.encoder.layer.2.intermediate.dense.weight', 'bert_model.encoder.layer.10.output.LayerNorm.bias', 'bert_model.encoder.layer.11.attention.output.dense.bias', 'bert_model.encoder.layer.9.attention.self.query.bias', 'bert_model.encoder.layer.1.attention.output.dense.bias', 'bert_model.encoder.layer.8.output.dense.weight', 'bert_model.encoder.layer.10.output.LayerNorm.weight', 'bert_model.encoder.layer.10.attention.output.LayerNorm.bias', 'bert_model.encoder.layer.7.intermediate.dense.bias', 'bert_model.encoder.layer.2.attention.self.query.weight', 'bert_model.encoder.layer.8.output.LayerNorm.bias', 'bert_model.encoder.layer.10.intermediate.dense.weight', 'bert_model.encoder.layer.11.attention.self.query.bias', 'bert_model.embeddings.word_embeddings.weight', 'bert_model.encoder.layer.6.output.LayerNorm.weight', 'bert_model.encoder.layer.3.intermediate.dense.bias', 'bert_model.encoder.layer.9.attention.output.LayerNorm.weight', 'bert_model.encoder.layer.1.output.dense.bias', 'bert_model.encoder.layer.9.output.LayerNorm.bias', 'bert_model.encoder.layer.1.output.LayerNorm.weight', 'bert_model.encoder.layer.1.intermediate.dense.bias', 'bert_model.encoder.layer.7.attention.self.query.weight', 'bert_model.encoder.layer.6.attention.output.LayerNorm.weight', 'bert_model.encoder.layer.8.output.LayerNorm.weight', 'bert_model.encoder.layer.10.attention.self.value.bias', 'bert_model.encoder.layer.0.attention.self.query.weight', 'bert_model.encoder.layer.4.attention.output.dense.weight', 'bert_model.encoder.layer.4.output.dense.weight', 'bert_model.encoder.layer.3.intermediate.dense.weight', 'bert_model.encoder.layer.7.output.dense.bias', 'bert_model.encoder.layer.7.output.LayerNorm.bias', 'bert_model.encoder.layer.1.attention.output.dense.weight', 'bert_model.encoder.layer.7.attention.output.LayerNorm.bias', 'bert_model.encoder.layer.6.attention.output.LayerNorm.bias', 'bert_model.encoder.layer.11.output.dense.bias', 'bert_model.encoder.layer.6.output.LayerNorm.bias', 'bert_model.encoder.layer.2.output.dense.weight', 'bert_model.encoder.layer.3.attention.output.LayerNorm.bias', 'bert_model.encoder.layer.11.attention.output.dense.weight', 'bert_model.encoder.layer.9.intermediate.dense.bias', 'bert_model.encoder.layer.4.attention.self.key.bias', 'bert_model.encoder.layer.0.attention.output.dense.bias', 'bert_model.encoder.layer.6.attention.self.query.bias', 'bert_model.encoder.layer.5.output.LayerNorm.weight', 'bert_model.encoder.layer.5.attention.output.dense.weight', 'bert_model.encoder.layer.9.output.dense.bias', 'bert_model.encoder.layer.11.attention.output.LayerNorm.weight', 'bert_model.encoder.layer.10.attention.self.query.bias', 'bert_model.encoder.layer.1.attention.self.query.weight', 'bert_model.encoder.layer.3.attention.self.key.bias', 'bert_model.encoder.layer.4.attention.output.LayerNorm.bias', 'bert_model.encoder.layer.5.intermediate.dense.weight', 'bert_model.encoder.layer.8.intermediate.dense.weight', 'bert_model.encoder.layer.5.attention.self.value.bias', 'bert_model.encoder.layer.11.attention.self.query.weight', 'bert_model.encoder.layer.11.intermediate.dense.weight', 'bert_model.encoder.layer.3.attention.self.query.weight', 'bert_model.encoder.layer.5.attention.self.query.bias', 'bert_model.encoder.layer.8.attention.self.query.bias', 'bert_model.encoder.layer.11.output.dense.weight', 'bert_model.encoder.layer.3.output.dense.bias', 'bert_model.encoder.layer.5.attention.self.value.weight', 'bert_model.encoder.layer.10.attention.output.dense.bias', 'bert_model.encoder.layer.2.attention.self.value.bias', 'bert_model.encoder.layer.3.attention.output.dense.weight', 'bert_model.embeddings.position_embeddings.weight', 'bert_model.encoder.layer.3.output.dense.weight', 'bert_model.encoder.layer.7.attention.output.dense.bias', 'bert_model.encoder.layer.0.attention.output.dense.weight', 'bert_model.encoder.layer.8.attention.output.LayerNorm.bias', 'bert_model.encoder.layer.9.intermediate.dense.weight', 'bert_model.embeddings.LayerNorm.weight', 'bert_model.encoder.layer.1.attention.self.value.bias', 'bert_model.encoder.layer.3.attention.self.value.weight', 'bert_model.encoder.layer.6.attention.self.key.bias', 'bert_model.encoder.layer.11.output.LayerNorm.weight', 'bert_model.encoder.layer.8.attention.output.dense.weight', 'bert_model.encoder.layer.9.attention.self.query.weight', 'bert_model.encoder.layer.7.attention.self.key.weight', 'bert_model.encoder.layer.4.output.dense.bias', 'bert_model.encoder.layer.9.attention.self.key.weight', 'bert_model.encoder.layer.6.intermediate.dense.weight', 'bert_model.encoder.layer.9.attention.output.dense.weight', 'bert_model.encoder.layer.6.output.dense.bias']\n", "You should probably TRAIN this model on a down-stream task to be able to use it for predictions and inference.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["📝 处理文本: '深度学习的应用有哪些？'\n", "\n", "🔧 阶段1: 分词器工作\n", "  分词: ['[UNK]', '[UNK]', '学', '[UNK]', '的', '[UNK]', '[UNK]', '有', '[UNK]', '[UNK]', '？']\n", "  转换为IDs: tensor([ 101,  100,  100, 1817,  100, 1916,  100,  100, 1873,  100])...\n", "\n", "🧠 阶段2: 编码器工作\n", "  生成语义向量: torch.<PERSON><PERSON>([1, 768])\n", "  向量范数: 27.7128\n", "\n", "✅ 总结:\n", "- 分词器：将文本转换为模型能处理的数字格式\n", "- 编码器：理解这些数字的语义含义，生成有意义的向量\n", "- 两者缺一不可，分工明确！\n"]}], "source": ["class DPRExample:\n", "    \"\"\"展示分词器和编码器如何配合工作\"\"\"\n", "    model_path = \"/Users/<USER>/Documents/Code/Experiment/robust-tableqa-main/dir\"\n", "    def __init__(self):\n", "        # 分词器：负责文本预处理,分词器的作用就是将文本转换为数字，并没有学习到语义\n", "        self.tokenizer = DPRQuestionEncoderTokenizer.from_pretrained(\n", "            self.model_path\n", "        )\n", "        \n", "        # 编码器：负责语义理解，将数字转换为语义向量\n", "        self.encoder = DPRQuestionEncoder.from_pretrained(\n", "            self.model_path\n", "        )\n", "    \n", "    def process_text(self, text):\n", "        \"\"\"完整的文本处理流程\"\"\"\n", "        print(f\"📝 处理文本: '{text}'\")\n", "        \n", "        # 阶段1: 分词器工作\n", "        print(\"\\n🔧 阶段1: 分词器工作\")\n", "        tokens = self.tokenizer.tokenize(text)\n", "        print(f\"  分词: {tokens}\")\n", "        \n", "        inputs = self.tokenizer(\n", "            text, \n", "            return_tensors=\"pt\", \n", "            padding=True, \n", "            truncation=True\n", "        )\n", "        print(f\"  转换为IDs: {inputs['input_ids'][0][:10]}...\")\n", "        \n", "        # 阶段2: 编码器工作\n", "        print(\"\\n🧠 阶段2: 编码器工作\")\n", "        with torch.no_grad():\n", "            outputs = self.encoder(**inputs)\n", "            embeddings = outputs.pooler_output\n", "        \n", "        print(f\"  生成语义向量: {embeddings.shape}\")\n", "        print(f\"  向量范数: {torch.norm(embeddings).item():.4f}\")\n", "        \n", "        return embeddings\n", "\n", "# 演示\n", "dpr_demo = DPRExample()\n", "embedding = dpr_demo.process_text(\"深度学习的应用有哪些？\")\n", "\n", "print(\"\\n✅ 总结:\")\n", "print(\"- 分词器：将文本转换为模型能处理的数字格式\")\n", "print(\"- 编码器：理解这些数字的语义含义，生成有意义的向量\")\n", "print(\"- 两者缺一不可，分工明确！\")"]}], "metadata": {"kernelspec": {"display_name": "tableqa", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.12"}}, "nbformat": 4, "nbformat_minor": 4}